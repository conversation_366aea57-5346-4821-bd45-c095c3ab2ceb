package cn.bztmaster.cnt.module.member.convert.tag;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.member.controller.admin.tag.vo.MemberTagCreateReqVO;
import cn.bztmaster.cnt.module.member.controller.admin.tag.vo.MemberTagRespVO;
import cn.bztmaster.cnt.module.member.controller.admin.tag.vo.MemberTagUpdateReqVO;
import cn.bztmaster.cnt.module.member.dal.dataobject.tag.MemberTagDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会员标签 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberTagConvert {

    MemberTagConvert INSTANCE = Mappers.getMapper(MemberTagConvert.class);

    MemberTagDO convert(MemberTagCreateReqVO bean);

    MemberTagDO convert(MemberTagUpdateReqVO bean);

    MemberTagRespVO convert(MemberTagDO bean);

    List<MemberTagRespVO> convertList(List<MemberTagDO> list);

    PageResult<MemberTagRespVO> convertPage(PageResult<MemberTagDO> page);

}
