package cn.bztmaster.cnt.module.system.api.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 - 文档素材新增/更新 Request DTO")
public class DocumentSaveReqDTO {
    @Schema(description = "文档ID", example = "1")
    private Long id;

    @Schema(description = "文档名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文档名称不能为空")
    private String documentName;

    @Schema(description = "文档URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文档URL不能为空")
    private String documentUrl;

    @Schema(description = "来源机构ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源机构ID不能为空")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "来源机构名称不能为空")
    private String sourceOrgName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 