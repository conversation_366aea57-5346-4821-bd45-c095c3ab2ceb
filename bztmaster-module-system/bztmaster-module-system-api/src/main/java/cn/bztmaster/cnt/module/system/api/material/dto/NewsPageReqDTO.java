package cn.bztmaster.cnt.module.system.api.material.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 图文素材分页 Request DTO")
public class NewsPageReqDTO extends PageParam {
    @Schema(description = "图文名称，模糊搜索")
    private String name;

    @Schema(description = "来源机构ID")
    private Long sourceOrgId;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 