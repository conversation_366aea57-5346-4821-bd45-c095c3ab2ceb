package cn.bztmaster.cnt.module.system.api.material.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "管理后台 - 文档素材 Response DTO")
public class DocumentRespDTO implements VO {
    @Schema(description = "文档ID", example = "1")
    private Long id;

    @Schema(description = "文档名称", example = "示例文档")
    private String documentName;

    @Schema(description = "文档URL", example = "https://example.com/document.pdf")
    private String documentUrl;

    @Schema(description = "来源机构ID", example = "1")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", example = "内部素材库")
    private String sourceOrgName;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;

    @Schema(description = "最后更新日期")
    private Date updateTime;
} 