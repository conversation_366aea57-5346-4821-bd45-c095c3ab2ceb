package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.ImageRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialImageApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 图片素材")
@AutoTrans(namespace = PREFIX, fields = {"name"})
public interface MaterialImageApi extends AutoTransable<ImageRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/image";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过图片 ID 查询图片")
    @Parameter(name = "id", description = "图片编号", example = "1", required = true)
    CommonResult<ImageRespDTO> getImage(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过图片 ID 查询图片们")
    @Parameter(name = "ids", description = "图片编号数组", example = "1,2", required = true)
    CommonResult<List<ImageRespDTO>> getImageList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-category")
    @Operation(summary = "通过分类 ID 查询图片们")
    @Parameter(name = "categoryId", description = "分类编号", example = "1", required = true)
    CommonResult<List<ImageRespDTO>> getImageListByCategoryId(@RequestParam("categoryId") Long categoryId);

    @GetMapping(PREFIX + "/list-by-org")
    @Operation(summary = "通过来源机构 ID 查询图片们")
    @Parameter(name = "sourceOrgId", description = "来源机构编号", example = "1", required = true)
    CommonResult<List<ImageRespDTO>> getImageListBySourceOrgId(@RequestParam("sourceOrgId") Long sourceOrgId);

    @GetMapping(PREFIX + "/recycleList")
    @Operation(summary = "图片回收站列表")
    CommonResult<List<ImageRespDTO>> getImageRecycleList(@RequestParam Map<String, Object> params);

    @PostMapping(PREFIX + "/recycleRestore")
    @Operation(summary = "图片回收站恢复")
    CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/recycleDelete")
    @Operation(summary = "图片回收站永久删除")
    CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList);

    /**
     * 获得图片 Map
     *
     * @param ids 图片编号数组
     * @return 图片 Map
     */
    default Map<Long, ImageRespDTO> getImageMap(Collection<Long> ids) {
        List<ImageRespDTO> images = getImageList(ids).getCheckedData();
        return CollectionUtils.convertMap(images, ImageRespDTO::getId);
    }

    /**
     * 校验图片是否有效。如下情况，视为无效：
     * 1. 图片编号不存在
     * 2. 图片被禁用
     *
     * @param id 图片编号
     */
    default void validateImage(Long id) {
        validateImageList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验图片们是否有效")
    @Parameter(name = "ids", description = "图片编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateImageList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<ImageRespDTO> selectByIds(List<?> ids) {
        return getImageList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default ImageRespDTO selectById(Object id) {
        return getImage(Convert.toLong(id)).getCheckedData();
    }
} 