package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialVideoService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/video")
@Tag(name = "素材库-视频管理")
@Slf4j
@Validated
public class MaterialVideoController {
    @Resource
    private MaterialVideoService videoService;

    @GetMapping("/list")
    @Operation(summary = "视频列表")
    public CommonResult<PageResult<VideoRespVO>> list(@Validated VideoPageReqVO reqVO) {
        return CommonResult.success(videoService.getVideoPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增视频")
    public CommonResult<Long> create(@RequestBody @Validated VideoSaveReqVO reqVO) {
        return CommonResult.success(videoService.createVideo(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑视频")
    public CommonResult<Boolean> update(@RequestBody @Validated VideoSaveReqVO reqVO) {
        videoService.updateVideo(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除视频")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        videoService.deleteVideo(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "视频详情")
    public CommonResult<VideoRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(videoService.getVideoDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "视频回收站列表")
    public CommonResult<PageResult<VideoRespVO>> recycleList(@Validated VideoPageReqVO reqVO,
                                                            @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                            @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(videoService.getVideoRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "视频回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        videoService.restoreVideoFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "视频回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        videoService.deleteVideoFromRecycle(idList);
        return CommonResult.success(true);
    }
} 