package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialImageDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialImageConvert {
    MaterialImageConvert INSTANCE = Mappers.getMapper(MaterialImageConvert.class);

    MaterialImageDO convert(ImageSaveReqVO bean);
    ImageRespVO convert(MaterialImageDO bean);
    List<ImageRespVO> convertList(List<MaterialImageDO> list);
    PageResult<ImageRespVO> convertPage(PageResult<MaterialImageDO> page);
} 