package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialImageService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/image")
@Tag(name = "素材库-图片管理")
@Slf4j
@Validated
public class MaterialImageController {
    @Resource
    private MaterialImageService imageService;

    @GetMapping("/list")
    @Operation(summary = "图片列表")
    public CommonResult<PageResult<ImageRespVO>> list(@Validated ImagePageReqVO reqVO) {
        return CommonResult.success(imageService.getImagePage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增图片")
    public CommonResult<Long> create(@RequestBody @Validated ImageSaveReqVO reqVO) {
        return CommonResult.success(imageService.createImage(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑图片")
    public CommonResult<Boolean> update(@RequestBody @Validated ImageSaveReqVO reqVO) {
        imageService.updateImage(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除图片")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        imageService.deleteImage(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "图片详情")
    public CommonResult<ImageRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(imageService.getImageDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "图片回收站列表")
    public CommonResult<PageResult<ImageRespVO>> recycleList(@Validated ImagePageReqVO reqVO,
                                                            @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                            @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(imageService.getImageRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "图片回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        imageService.restoreImageFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "图片回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        imageService.deleteImageFromRecycle(idList);
        return CommonResult.success(true);
    }
} 