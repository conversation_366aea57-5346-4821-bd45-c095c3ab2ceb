package cn.bztmaster.cnt.module.system.dal.dataobject.material;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("mp_material_category")
@Schema(description = "素材分类 DO")
public class MaterialCategoryDO {
    @TableId
    private Long id;
    private String name;
    private Long parentId;
    private Integer sort;
    private Integer status;
    private String description;
    private String icon;
    private Integer level;
    private String path;
    /** 可视范围机构ID */
    private Long visibleOrgId;
    /** 可视范围机构名称 */
    private String visibleOrgName;
    private Long tenantId;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 