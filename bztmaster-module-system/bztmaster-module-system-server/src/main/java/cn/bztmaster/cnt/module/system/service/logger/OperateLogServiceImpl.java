package cn.bztmaster.cnt.module.system.service.logger;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.common.biz.system.logger.dto.OperateLogCreateReqDTO;
import cn.bztmaster.cnt.module.system.api.logger.dto.OperateLogPageReqDTO;
import cn.bztmaster.cnt.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import cn.bztmaster.cnt.module.system.dal.dataobject.logger.OperateLogDO;
import cn.bztmaster.cnt.module.system.dal.mysql.logger.OperateLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OperateLogServiceImpl implements OperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;

    @Override
    public void createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        OperateLogDO log = BeanUtils.toBean(createReqDTO, OperateLogDO.class);
        operateLogMapper.insert(log);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqVO pageReqVO) {
        return operateLogMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        return operateLogMapper.selectPage(pageReqDTO);
    }

}
