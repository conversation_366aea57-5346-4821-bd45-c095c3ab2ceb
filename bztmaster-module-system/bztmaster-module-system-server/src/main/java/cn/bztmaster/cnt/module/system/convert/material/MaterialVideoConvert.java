package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVideoDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialVideoConvert {
    MaterialVideoConvert INSTANCE = Mappers.getMapper(MaterialVideoConvert.class);

    MaterialVideoDO convert(VideoSaveReqVO bean);
    VideoRespVO convert(MaterialVideoDO bean);
    List<VideoRespVO> convertList(List<MaterialVideoDO> list);
    PageResult<VideoRespVO> convertPage(PageResult<MaterialVideoDO> page);
} 