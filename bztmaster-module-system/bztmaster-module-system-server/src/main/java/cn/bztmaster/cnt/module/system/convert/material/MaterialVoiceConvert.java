package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVoiceDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialVoiceConvert {
    MaterialVoiceConvert INSTANCE = Mappers.getMapper(MaterialVoiceConvert.class);

    MaterialVoiceDO convert(VoiceSaveReqVO bean);
    VoiceRespVO convert(MaterialVoiceDO bean);
    List<VoiceRespVO> convertList(List<MaterialVoiceDO> list);
    PageResult<VoiceRespVO> convertPage(PageResult<MaterialVoiceDO> page);
}
