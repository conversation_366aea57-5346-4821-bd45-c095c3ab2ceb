package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVideoDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.VideoPageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialVideoMapper extends BaseMapperX<MaterialVideoDO> {

    default PageResult<MaterialVideoDO> selectPage(VideoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialVideoDO>()
                .likeIfPresent(MaterialVideoDO::getName, reqVO.getName())
                .eqIfPresent(MaterialVideoDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialVideoDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialVideoDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialVideoDO::getDeleted, false)
                .orderByDesc(MaterialVideoDO::getId));
    }

    default List<MaterialVideoDO> selectListByCategoryId(Long categoryId) {
        return selectList(new LambdaQueryWrapperX<MaterialVideoDO>()
                .eq(MaterialVideoDO::getCategoryId, categoryId)
                .orderByDesc(MaterialVideoDO::getId));
    }

    default List<MaterialVideoDO> selectListBySourceOrgId(Long sourceOrgId) {
        return selectList(new LambdaQueryWrapperX<MaterialVideoDO>()
                .eq(MaterialVideoDO::getSourceOrgId, sourceOrgId)
                .orderByDesc(MaterialVideoDO::getId));
    }

    default PageResult<MaterialVideoDO> selectRecyclePage(VideoPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialVideoDO>()
                .likeIfPresent(MaterialVideoDO::getName, reqVO.getName())
                .eqIfPresent(MaterialVideoDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialVideoDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialVideoDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialVideoDO::getDeleted, true)
                .between(MaterialVideoDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialVideoDO::getUpdateTime));
    }
    
    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_video SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}