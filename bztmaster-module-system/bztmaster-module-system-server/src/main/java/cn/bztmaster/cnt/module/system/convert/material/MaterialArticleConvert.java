package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialArticleDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialArticleConvert {
    MaterialArticleConvert INSTANCE = Mappers.getMapper(MaterialArticleConvert.class);

    MaterialArticleDO convert(ArticleSaveReqVO bean);
    ArticleRespVO convert(MaterialArticleDO bean);
    List<ArticleRespVO> convertList(List<MaterialArticleDO> list);
    PageResult<ArticleRespVO> convertPage(PageResult<MaterialArticleDO> page);
} 