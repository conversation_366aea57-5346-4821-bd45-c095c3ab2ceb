package cn.bztmaster.cnt.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 - 视频素材新增/更新 Request VO")
public class VideoSaveReqVO {
    @Schema(description = "视频ID", example = "1")
    private Long id;

    @Schema(description = "视频名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "视频名称不能为空")
    private String name;

    @Schema(description = "视频URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "视频URL不能为空")
    private String url;

    @Schema(description = "缩略图URL", example = "https://example.com/thumb.jpg")
    private String thumbUrl;

    @Schema(description = "来源机构ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源机构ID不能为空")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "来源机构名称不能为空")
    private String sourceOrgName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 