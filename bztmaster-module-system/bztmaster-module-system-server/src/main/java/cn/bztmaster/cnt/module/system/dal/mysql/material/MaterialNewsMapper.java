package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialNewsDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.NewsPageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialNewsMapper extends BaseMapperX<MaterialNewsDO> {

    default PageResult<MaterialNewsDO> selectPage(NewsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialNewsDO>()
                .likeIfPresent(MaterialNewsDO::getName, reqVO.getName())
                .eqIfPresent(MaterialNewsDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialNewsDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialNewsDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialNewsDO::getDeleted, false)
                .orderByDesc(MaterialNewsDO::getId));
    }

    default PageResult<MaterialNewsDO> selectRecyclePage(NewsPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialNewsDO>()
                .likeIfPresent(MaterialNewsDO::getName, reqVO.getName())
                .eqIfPresent(MaterialNewsDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialNewsDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialNewsDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialNewsDO::getDeleted, true)
                .between(MaterialNewsDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialNewsDO::getUpdateTime));
    }

    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_news SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}