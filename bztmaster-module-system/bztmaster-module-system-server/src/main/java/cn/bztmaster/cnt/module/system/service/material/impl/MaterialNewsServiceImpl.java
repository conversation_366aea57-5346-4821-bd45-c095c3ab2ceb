package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialNewsDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialNewsMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialNewsService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialNewsConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class MaterialNewsServiceImpl implements MaterialNewsService {
    @Resource
    private MaterialNewsMapper newsMapper;
    @Resource
    private MaterialNewsConvert newsConvert;

    @Override
    public PageResult<NewsRespVO> getNewsPage(NewsPageReqVO reqVO) {
        PageResult<MaterialNewsDO> pageResult = newsMapper.selectPage(reqVO);
        return newsConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createNews(NewsSaveReqVO reqVO) {
        MaterialNewsDO news = newsConvert.convert(reqVO);
        newsMapper.insert(news);
        return news.getId();
    }

    @Override
    @Transactional
    public void updateNews(NewsSaveReqVO reqVO) {
        MaterialNewsDO news = newsConvert.convert(reqVO);
        newsMapper.updateById(news);
    }

    @Override
    @Transactional
    public void deleteNews(Long id) {
        newsMapper.logicDeleteById(id);
    }

    @Override
    public NewsRespVO getNewsDetail(Long id) {
        MaterialNewsDO news = newsMapper.selectById(id);
        if (news == null || Boolean.TRUE.equals(news.getDeleted())) return null;
        return newsConvert.convert(news);
    }

    @Override
    public PageResult<NewsRespVO> getNewsRecyclePage(NewsPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialNewsDO> pageResult = newsMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return newsConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreNewsFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        newsMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteNewsFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        newsMapper.deleteFromRecycle(idList);
    }
} 