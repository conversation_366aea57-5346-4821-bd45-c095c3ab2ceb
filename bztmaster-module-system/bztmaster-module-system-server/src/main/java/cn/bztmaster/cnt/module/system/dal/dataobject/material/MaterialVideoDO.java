package cn.bztmaster.cnt.module.system.dal.dataobject.material;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("mp_material_video")
@Schema(description = "视频素材 DO")
public class MaterialVideoDO {
    @TableId
    private Long id;
    private String name;
    private Long categoryId;
    private String mediaId;
    private String url;
    private String localUrl;
    private String fileName;
    private Long fileSize;
    private String fileType;
    private Integer duration;
    private Integer width;
    private Integer height;
    private String format;
    private String title;
    private String introduction;
    private String thumbMediaId;
    private String thumbUrl;
    private Integer sourceType;
    private Long sourceOrgId;
    private String sourceOrgName;
    private Long accountId;
    private Integer status;
    private String tags;
    private Boolean isPermanent;
    private Date expireTime;
    /** 可视范围机构ID */
    private Long visibleOrgId;
    /** 可视范围机构名称 */
    private String visibleOrgName;
    private Long tenantId;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 