package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialCategoryConvert {
    MaterialCategoryConvert INSTANCE = Mappers.getMapper(MaterialCategoryConvert.class);

    MaterialCategoryDO convert(CategoryCreateReqVO bean);
    MaterialCategoryDO convert(CategoryUpdateReqVO bean);
    CategoryListRespVO convert(MaterialCategoryDO bean);
    List<CategoryListRespVO> convertList(List<MaterialCategoryDO> list);
} 