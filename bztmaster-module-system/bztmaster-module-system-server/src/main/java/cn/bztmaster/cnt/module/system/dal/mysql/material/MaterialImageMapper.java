package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialImageDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.ImagePageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialImageMapper extends BaseMapperX<MaterialImageDO> {

    default PageResult<MaterialImageDO> selectPage(ImagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialImageDO>()
                .likeIfPresent(MaterialImageDO::getName, reqVO.getName())
                .eqIfPresent(MaterialImageDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialImageDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialImageDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialImageDO::getDeleted, false)
                .orderByDesc(MaterialImageDO::getId));
    }

    default PageResult<MaterialImageDO> selectRecyclePage(ImagePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialImageDO>()
                .likeIfPresent(MaterialImageDO::getName, reqVO.getName())
                .eqIfPresent(MaterialImageDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialImageDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialImageDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialImageDO::getDeleted, true)
                .between(MaterialImageDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialImageDO::getUpdateTime));
    }

    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_image SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}