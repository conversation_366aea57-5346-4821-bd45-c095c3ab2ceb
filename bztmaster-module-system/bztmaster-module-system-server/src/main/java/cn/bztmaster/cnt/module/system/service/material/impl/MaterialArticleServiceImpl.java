package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialArticleDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialArticleMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialArticleService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialArticleConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class MaterialArticleServiceImpl implements MaterialArticleService {
    @Resource
    private MaterialArticleMapper articleMapper;
    @Resource
    private MaterialArticleConvert articleConvert;

    @Override
    public PageResult<ArticleRespVO> getArticlePage(ArticlePageReqVO reqVO) {
        PageResult<MaterialArticleDO> pageResult = articleMapper.selectPage(reqVO);
        return articleConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createArticle(ArticleSaveReqVO reqVO) {
        MaterialArticleDO article = articleConvert.convert(reqVO);
        articleMapper.insert(article);
        return article.getId();
    }

    @Override
    @Transactional
    public void updateArticle(ArticleSaveReqVO reqVO) {
        MaterialArticleDO article = articleConvert.convert(reqVO);
        articleMapper.updateById(article);
    }

    @Override
    @Transactional
    public void deleteArticle(Long id) {
        articleMapper.logicDeleteById(id);
    }

    @Override
    public ArticleRespVO getArticleDetail(Long id) {
        MaterialArticleDO article = articleMapper.selectById(id);
        if (article == null || Boolean.TRUE.equals(article.getDeleted())) return null;
        return articleConvert.convert(article);
    }

    @Override
    public PageResult<ArticleRespVO> getArticleRecyclePage(ArticlePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialArticleDO> pageResult = articleMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return articleConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreArticleFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        articleMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteArticleFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        articleMapper.deleteFromRecycle(idList);
    }
} 