package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVoiceDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialVoiceMapper;
import cn.bztmaster.cnt.module.system.convert.material.MaterialVoiceConvert;
import cn.bztmaster.cnt.module.system.service.material.MaterialVoiceService;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

@Service
public class MaterialVoiceServiceImpl implements MaterialVoiceService {
    @Resource
    private MaterialVoiceMapper voiceMapper;
    @Resource
    private MaterialVoiceConvert voiceConvert;

    @Override
    public PageResult<VoiceRespVO> getVoicePage(VoicePageReqVO reqVO) {
        PageResult<MaterialVoiceDO> pageResult = voiceMapper.selectPage(reqVO);
        return voiceConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createVoice(VoiceSaveReqVO reqVO) {
        MaterialVoiceDO voice = voiceConvert.convert(reqVO);
        voiceMapper.insert(voice);
        return voice.getId();
    }

    @Override
    @Transactional
    public void updateVoice(VoiceSaveReqVO reqVO) {
        MaterialVoiceDO voice = voiceConvert.convert(reqVO);
        voiceMapper.updateById(voice);
    }

    @Override
    @Transactional
    public void deleteVoice(Long id) {
        voiceMapper.logicDeleteById(id);
    }

    @Override
    public VoiceRespVO getVoiceDetail(Long id) {
        MaterialVoiceDO voice = voiceMapper.selectById(id);
        if (voice == null || Boolean.TRUE.equals(voice.getDeleted())) return null;
        return voiceConvert.convert(voice);
    }

    @Override
    public MaterialVoiceDO getVoice(Long id) {
        return voiceMapper.selectById(id);
    }

    @Override
    public List<MaterialVoiceDO> getVoiceList(Collection<Long> ids) {
        return voiceMapper.selectBatchIds(ids);
    }

    @Override
    public List<MaterialVoiceDO> getVoiceListByCategoryId(Long categoryId) {
        return voiceMapper.selectListByCategoryId(categoryId);
    }

    @Override
    public List<MaterialVoiceDO> getVoiceListBySourceOrgId(Long sourceOrgId) {
        return voiceMapper.selectListBySourceOrgId(sourceOrgId);
    }

    @Override
    public void validateVoiceList(Collection<Long> ids) {
        List<MaterialVoiceDO> voices = voiceMapper.selectBatchIds(ids);
        if (voices.size() != ids.size()) {
            throw new RuntimeException("存在无效的音频ID");
        }
        // 可以添加其他验证逻辑，比如检查状态等
    }

    @Override
    public PageResult<VoiceRespVO> getVoiceRecyclePage(VoicePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialVoiceDO> pageResult = voiceMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return voiceConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreVoiceFromRecycle(List<Long> idList) {
        voiceMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteVoiceFromRecycle(List<Long> idList) {
        voiceMapper.deleteFromRecycle(idList);
    }
}
