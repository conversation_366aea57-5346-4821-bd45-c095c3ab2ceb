package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialDocumentDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.DocumentPageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialDocumentMapper extends BaseMapperX<MaterialDocumentDO> {

    default PageResult<MaterialDocumentDO> selectPage(DocumentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialDocumentDO>()
                .likeIfPresent(MaterialDocumentDO::getDocumentName, reqVO.getDocumentName())
                .eqIfPresent(MaterialDocumentDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialDocumentDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialDocumentDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialDocumentDO::getDeleted, false)
                .orderByDesc(MaterialDocumentDO::getId));
    }

    default PageResult<MaterialDocumentDO> selectRecyclePage(DocumentPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialDocumentDO>()
                .likeIfPresent(MaterialDocumentDO::getDocumentName, reqVO.getDocumentName())
                .eqIfPresent(MaterialDocumentDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialDocumentDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialDocumentDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialDocumentDO::getDeleted, true)
                .between(MaterialDocumentDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialDocumentDO::getUpdateTime));
    }

    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_document SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}