package cn.bztmaster.cnt.module.system.dal.dataobject.material;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("mp_material_article")
@Schema(description = "文章素材 DO")
public class MaterialArticleDO {
    @TableId
    private Long id;
    private String title;
    private Long categoryId;
    private String content;
    private String contentText;
    private String summary;
    private String author;
    private String coverUrl;
    private String coverMediaId;
    private Integer sourceType;
    private Long sourceOrgId;
    private String sourceOrgName;
    private String sourceUrl;
    private Long accountId;
    private Integer status;
    private Date publishTime;
    private String tags;
    private String keywords;
    private Long readCount;
    private Long likeCount;
    private Long commentCount;
    private Long shareCount;
    private Boolean isTop;
    private Boolean isRecommend;
    private Integer sort;
    /** 可视范围机构ID */
    private Long visibleOrgId;
    /** 可视范围机构名称 */
    private String visibleOrgName;
    private Long tenantId;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 