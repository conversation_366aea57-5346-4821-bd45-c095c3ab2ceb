package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialCategoryDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialCategoryMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialCategoryService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialCategoryConvert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

@Service
public class MaterialCategoryServiceImpl implements MaterialCategoryService {
    @Resource
    private MaterialCategoryMapper categoryMapper;
    @Resource
    private MaterialCategoryConvert categoryConvert;

    @Override
    public List<CategoryListRespVO> getCategoryList() {
        List<MaterialCategoryDO> list = categoryMapper.selectList(null);
        return categoryConvert.convertList(list);
    }

    @Override
    @Transactional
    public Long createCategory(CategoryCreateReqVO reqVO) {
        MaterialCategoryDO category = categoryConvert.convert(reqVO);
        categoryMapper.insert(category);
        return category.getId();
    }

    @Override
    @Transactional
    public void updateCategory(CategoryUpdateReqVO reqVO) {
        MaterialCategoryDO category = categoryConvert.convert(reqVO);
        categoryMapper.updateById(category);
    }

    @Override
    @Transactional
    public void deleteCategory(Long id) {
        categoryMapper.deleteById(id);
    }
} 