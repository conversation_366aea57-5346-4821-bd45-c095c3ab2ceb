package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialDocumentDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialDocumentConvert {
    MaterialDocumentConvert INSTANCE = Mappers.getMapper(MaterialDocumentConvert.class);

    MaterialDocumentDO convert(DocumentSaveReqVO bean);
    DocumentRespVO convert(MaterialDocumentDO bean);
    List<DocumentRespVO> convertList(List<MaterialDocumentDO> list);
    PageResult<DocumentRespVO> convertPage(PageResult<MaterialDocumentDO> page);
} 