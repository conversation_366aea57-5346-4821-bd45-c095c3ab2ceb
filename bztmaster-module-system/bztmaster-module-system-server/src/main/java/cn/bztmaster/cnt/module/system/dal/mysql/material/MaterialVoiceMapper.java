package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVoiceDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.VoicePageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialVoiceMapper extends BaseMapperX<MaterialVoiceDO> {

    default PageResult<MaterialVoiceDO> selectPage(VoicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialVoiceDO>()
                .likeIfPresent(MaterialVoiceDO::getName, reqVO.getName())
                .eqIfPresent(MaterialVoiceDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialVoiceDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialVoiceDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialVoiceDO::getDeleted, false)
                .orderByDesc(MaterialVoiceDO::getId));
    }

    default List<MaterialVoiceDO> selectListByCategoryId(Long categoryId) {
        return selectList(new LambdaQueryWrapperX<MaterialVoiceDO>()
                .eq(MaterialVoiceDO::getCategoryId, categoryId)
                .orderByDesc(MaterialVoiceDO::getId));
    }

    default List<MaterialVoiceDO> selectListBySourceOrgId(Long sourceOrgId) {
        return selectList(new LambdaQueryWrapperX<MaterialVoiceDO>()
                .eq(MaterialVoiceDO::getSourceOrgId, sourceOrgId)
                .orderByDesc(MaterialVoiceDO::getId));
    }

    default PageResult<MaterialVoiceDO> selectRecyclePage(VoicePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialVoiceDO>()
                .likeIfPresent(MaterialVoiceDO::getName, reqVO.getName())
                .eqIfPresent(MaterialVoiceDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialVoiceDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialVoiceDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialVoiceDO::getDeleted, true)
                .between(MaterialVoiceDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialVoiceDO::getUpdateTime));
    }
    
    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_voice SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}
