package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialArticleService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/article")
@Tag(name = "素材库-文章管理")
@Slf4j
@Validated
public class MaterialArticleController {
    @Resource
    private MaterialArticleService articleService;

    @GetMapping("/list")
    @Operation(summary = "文章列表")
    public CommonResult<PageResult<ArticleRespVO>> list(@Validated ArticlePageReqVO reqVO) {
        return CommonResult.success(articleService.getArticlePage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增文章")
    public CommonResult<Long> create(@RequestBody @Validated ArticleSaveReqVO reqVO) {
        return CommonResult.success(articleService.createArticle(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑文章")
    public CommonResult<Boolean> update(@RequestBody @Validated ArticleSaveReqVO reqVO) {
        articleService.updateArticle(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除文章")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        articleService.deleteArticle(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "文章详情")
    public CommonResult<ArticleRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(articleService.getArticleDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "文章回收站列表")
    public CommonResult<PageResult<ArticleRespVO>> recycleList(@Validated ArticlePageReqVO reqVO,
                                                              @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                              @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(articleService.getArticleRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "文章回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        articleService.restoreArticleFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "文章回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        articleService.deleteArticleFromRecycle(idList);
        return CommonResult.success(true);
    }
} 