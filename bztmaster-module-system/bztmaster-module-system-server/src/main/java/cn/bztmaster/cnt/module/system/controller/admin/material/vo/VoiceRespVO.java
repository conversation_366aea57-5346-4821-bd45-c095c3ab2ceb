package cn.bztmaster.cnt.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "管理后台 - 音频素材 Response VO")
public class VoiceRespVO {
    @Schema(description = "音频ID", example = "1")
    private Long id;

    @Schema(description = "音频名称", example = "示例音频")
    private String name;

    @Schema(description = "音频URL", example = "https://example.com/voice.mp3")
    private String url;

    @Schema(description = "来源机构ID", example = "1")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", example = "内部素材库")
    private String sourceOrgName;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "最后更新日期")
    private Date updateTime;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "时长（秒）")
    private Integer duration;

    @Schema(description = "格式")
    private String format;
}
