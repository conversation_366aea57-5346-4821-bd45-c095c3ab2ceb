<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bztmaster</artifactId>
        <groupId>cn.bztmaster.cnt</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bztmaster-module-mall</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <description>
        商城大模块，由 product 商品、promotion 营销、trade 交易、statistics 统计等组成
    </description>
    <modules>
        <module>bztmaster-module-product-api</module>
        <module>bztmaster-module-product-server</module>
        <module>bztmaster-module-promotion-api</module>
        <module>bztmaster-module-promotion-server</module>
        <module>bztmaster-module-trade-api</module>
        <module>bztmaster-module-trade-server</module>
        <module>bztmaster-module-statistics-api</module>
        <module>bztmaster-module-statistics-server</module>
    </modules>

</project>
