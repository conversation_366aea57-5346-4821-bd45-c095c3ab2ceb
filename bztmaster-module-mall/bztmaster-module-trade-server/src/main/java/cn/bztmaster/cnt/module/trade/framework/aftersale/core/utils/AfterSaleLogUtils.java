package cn.bztmaster.cnt.module.trade.framework.aftersale.core.utils;


import cn.bztmaster.cnt.module.trade.enums.aftersale.AfterSaleOperateTypeEnum;
import cn.bztmaster.cnt.module.trade.framework.aftersale.core.aop.AfterSaleLogAspect;

import java.util.Map;

/**
 * 操作日志工具类
 * 目前主要的作用，是提供给业务代码，记录操作明细和拓展字段
 *
 * <AUTHOR>
 */
public class AfterSaleLogUtils {

    public static void setAfterSaleOperateType(AfterSaleOperateTypeEnum operateType) {
        AfterSaleLogAspect.setAfterSaleOperateType(operateType);
    }

    public static void setAfterSaleInfo(Long id, Integer beforeStatus, Integer afterStatus) {
        setAfterSaleInfo(id, beforeStatus, afterStatus, null);
    }

    public static void setAfterSaleInfo(Long id, Integer beforeStatus, Integer afterStatus,
                                        Map<String, Object> exts) {
        AfterSaleLogAspect.setAfterSale(id, beforeStatus, afterStatus, exts);
    }

}
