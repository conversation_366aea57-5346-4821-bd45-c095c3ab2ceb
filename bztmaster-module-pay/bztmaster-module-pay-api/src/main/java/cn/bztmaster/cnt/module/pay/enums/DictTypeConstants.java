package cn.bztmaster.cnt.module.pay.enums;

/**
 * Pay 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String CHANNEL_CODE = "pay_channel_code"; // 支付渠道编码

    String ORDER_STATUS = "pay_order_status"; // 支付渠道

    String REFUND_STATUS = "pay_order_status"; // 退款状态

    String NOTIFY_STATUS = "pay_notify_status"; // 回调状态

    String TRANSFER_STATUS = "pay_transfer_status"; // 转账状态

}
