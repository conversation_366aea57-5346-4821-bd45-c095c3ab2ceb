package cn.bztmaster.cnt.module.pay.dal.mysql.channel;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.pay.dal.dataobject.channel.PayChannelDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Mapper
public interface PayChannelMapper extends BaseMapperX<PayChannelDO> {

    default PayChannelDO selectByAppIdAndCode(Long appId, String code) {
        return selectOne(PayChannelDO::getAppId, appId, PayChannelDO::getCode, code);
    }

    default List<PayChannelDO> selectListByAppIds(Collection<Long> appIds){
        return selectList(PayChannelDO::getAppId, appIds);
    }

    default List<PayChannelDO> selectListByAppId(Long appId, Integer status) {
        return selectList(new LambdaQueryWrapperX<PayChannelDO>()
                .eq(PayChannelDO::getAppId, appId)
                .eq(PayChannelDO::getStatus, status));
    }

}
