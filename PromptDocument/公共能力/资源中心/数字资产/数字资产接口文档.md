# 资源中心-数字资产(课程)接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/digital-asset/course`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求头**: Content-Type: application/json

### 1.2 统一响应格式

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

**响应字段说明:**

| 字段名    | 类型    | 说明                           |
| --------- | ------- | ------------------------------ |
| code      | Integer | 响应码，0表示成功，非0表示失败 |
| msg       | String  | 响应消息                       |
| data      | Object  | 响应数据                       |
| timestamp | Long    | 响应时间戳                     |

## 2. 课程管理接口

### 2.1 分页查询课程列表

**接口地址**: `GET /page`

**功能说明**: 分页查询数字资产课程列表，支持多条件筛选

**请求参数**:

| 参数名         | 类型    | 必填 | 说明                                                       |
| -------------- | ------- | ---- | ---------------------------------------------------------- |
| pageNo         | Integer | 是   | 页码，从1开始                                              |
| pageSize       | Integer | 是   | 每页大小，最大100                                          |
| category       | String  | 否   | 课程分类：家政技能、职业素养、高校实践、企业管理           |
| status         | String  | 否   | 课程状态：待发布、已上架、已下架                           |
| teachType      | String  | 否   | 授课方式：线上授课、线下授课                               |
| businessModule | String  | 否   | 业务板块：家政服务、高校实践、培训管理、就业服务、兼职零工 |
| merchant       | Long    | 否   | 收款商户ID                                                 |
| keyword        | String  | 否   | 课程名称关键词搜索                                         |

**响应字段**:

| 字段名                | 类型    | 说明         |
| --------------------- | ------- | ------------ |
| total                 | Long    | 总记录数     |
| list                  | Array   | 课程列表     |
| list[].id             | Long    | 课程ID       |
| list[].name           | String  | 课程名称     |
| list[].teachType      | String  | 授课方式     |
| list[].coverUrl       | String  | 课程封面URL  |
| list[].category       | String  | 课程分类     |
| list[].status         | String  | 课程状态     |
| list[].teacherId      | Long    | 关联讲师ID   |
| list[].teacherName    | String  | 关联讲师名称 |
| list[].businessModule | String  | 所属业务板块 |
| list[].merchant       | Long    | 收款商户ID   |
| list[].merchantName   | String  | 收款商户名称 |
| list[].enrolledCount  | Integer | 已报名人数   |
| list[].createTime     | String  | 创建时间     |

**请求示例**:

```http
GET /publicbiz/digital-asset/course/page?pageNo=1&pageSize=10&category=家政技能&status=已上架
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "name": "金牌月嫂职业技能培训",
        "teachType": "线下授课",
        "coverUrl": "https://example.com/cover1.jpg",
        "category": "家政技能",
        "status": "已上架",
        "teacherId": 1001,
        "teacherName": "王老师",
        "businessModule": "家政服务",
        "merchant": 1001,
        "merchantName": "汇成家政服务",
        "enrolledCount": 25,
        "createTime": "2024-01-15 10:30:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 2.2 新增课程

**接口地址**: `POST /add`

**功能说明**: 新增数字资产课程

**请求参数**:

| 参数名         | 类型       | 必填 | 说明                                             |
| -------------- | ---------- | ---- | ------------------------------------------------ |
| name           | String     | 是   | 课程名称，最大200字符                            |
| teachType      | String     | 是   | 授课方式：线上授课、线下授课                     |
| coverUrl       | String     | 否   | 课程封面URL                                      |
| category       | String     | 是   | 课程分类：家政技能、职业素养、高校实践、企业管理 |
| status         | String     | 是   | 课程状态：待发布、已上架、已下架                 |
| teacherId      | Long       | 否   | 关联讲师ID                                       |
| teacherName    | String     | 否   | 关联讲师名称                                     |
| businessModule | String     | 否   | 所属业务板块                                     |
| merchant       | Long       | 否   | 收款商户ID                                       |
| merchantName   | String     | 否   | 收款商户名称                                     |
| description    | String     | 否   | 课程详情介绍                                     |
| location       | String     | 否   | 上课地点（线下授课专用）                         |
| schedule       | String     | 否   | 排期安排（线下授课专用）                         |
| totalSeats     | Integer    | 否   | 总名额（线下授课专用）                           |
| enrolledCount  | Integer    | 否   | 已报名人数                                       |
| totalDuration  | BigDecimal | 否   | 课程总时长（小时，线上授课专用）                 |

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| id     | Long | 新增课程的ID |

**请求示例**:

```json
POST /publicbiz/digital-asset/course/add
Content-Type: application/json

{
  "name": "高级育婴师培训课程",
  "teachType": "线下授课",
  "coverUrl": "https://example.com/cover.jpg",
  "category": "家政技能",
  "status": "待发布",
  "teacherId": 1002,
  "teacherName": "李老师",
  "businessModule": "家政服务",
  "merchant": 1001,
  "merchantName": "汇成家政服务",
  "description": "专业的高级育婴师培训课程",
  "location": "北京市朝阳区培训中心",
  "schedule": "每周一、三、五 9:00-17:00",
  "totalSeats": 30,
  "enrolledCount": 0
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 123
  },
  "timestamp": 1640995200000
}
```

### 2.3 获取课程详情

**接口地址**: `GET /get/{id}`

**功能说明**: 根据课程ID获取课程详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 课程ID（路径参数） |

**响应字段**: 与新增课程请求参数相同，另外包含：

| 字段名     | 类型   | 说明     |
| ---------- | ------ | -------- |
| id         | Long   | 课程ID   |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |

**请求示例**:

```http
GET /publicbiz/digital-asset/course/get/123
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 123,
    "name": "高级育婴师培训课程",
    "teachType": "线下授课",
    "coverUrl": "https://example.com/cover.jpg",
    "category": "家政技能",
    "status": "待发布",
    "teacherId": 1002,
    "teacherName": "李老师",
    "businessModule": "家政服务",
    "merchant": 1001,
    "merchantName": "汇成家政服务",
    "description": "专业的高级育婴师培训课程",
    "location": "北京市朝阳区培训中心",
    "schedule": "每周一、三、五 9:00-17:00",
    "totalSeats": 30,
    "enrolledCount": 0,
    "createTime": "2024-01-15 10:30:00",
    "updateTime": "2024-01-15 10:30:00"
  },
  "timestamp": 1640995200000
}
```

### 2.4 更新课程信息

**接口地址**: `PUT /update`

**功能说明**: 更新课程基本信息

**请求参数**: 与新增课程相同，另外包含：

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 课程ID |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```json
PUT /publicbiz/digital-asset/course/update
Content-Type: application/json

{
  "id": 123,
  "name": "高级育婴师培训课程（更新版）",
  "teachType": "线下授课",
  "coverUrl": "https://example.com/new-cover.jpg",
  "category": "家政技能",
  "status": "已上架",
  "teacherId": 1002,
  "teacherName": "李老师",
  "businessModule": "家政服务",
  "merchant": 1001,
  "merchantName": "汇成家政服务",
  "description": "专业的高级育婴师培训课程，内容已更新",
  "location": "北京市朝阳区培训中心",
  "schedule": "每周一、三、五 9:00-17:00",
  "totalSeats": 35,
  "enrolledCount": 5
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 2.5 课程状态管理

**接口地址**: `PUT /status/{id}`

**功能说明**: 修改课程状态（上架/下架）

**请求参数**:

| 参数名 | 类型   | 必填 | 说明                     |
| ------ | ------ | ---- | ------------------------ |
| id     | Long   | 是   | 课程ID（路径参数）       |
| status | String | 是   | 课程状态：已上架、已下架 |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```json
PUT /publicbiz/digital-asset/course/status/123
Content-Type: application/json

{
  "status": "已上架"
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

## 3. 课程章节管理接口（线上课程专用）

### 3.1 获取课程章节列表

**接口地址**: `GET /chapter/list/{courseId}`

**功能说明**: 获取指定课程的章节列表（包含每个章节下的课时列表）

**请求参数**:

| 参数名   | 类型 | 必填 | 说明               |
| -------- | ---- | ---- | ------------------ |
| courseId | Long | 是   | 课程ID（路径参数） |

**响应字段**:

| 字段名                           | 类型    | 说明                       |
| -------------------------------- | ------- | -------------------------- |
| list                             | Array   | 章节列表                   |
| list[].id                        | Long    | 章节ID                     |
| list[].courseId                  | Long    | 课程ID                     |
| list[].title                     | String  | 章节标题                   |
| list[].sortOrder                 | Integer | 排序序号                   |
| list[].createTime                | String  | 创建时间                   |
| list[].lessons                   | Array   | 该章节下的课时列表         |
| list[].lessons[].id              | Long    | 课时ID                     |
| list[].lessons[].chapterId       | Long    | 章节ID                     |
| list[].lessons[].title           | String  | 课时标题                   |
| list[].lessons[].lessonType      | String  | 课时类型：视频、文档、音频 |
| list[].lessons[].isFree          | Boolean | 是否免费试看               |
| list[].lessons[].materialId      | String  | 关联素材ID                 |
| list[].lessons[].materialName    | String  | 关联素材名称               |
| list[].lessons[].materialFileUrl | String  | 关联素材文件URL            |
| list[].lessons[].sortOrder       | Integer | 排序序号                   |
| list[].lessons[].createTime      | String  | 创建时间                   |

**请求示例**:

```http
GET /publicbiz/digital-asset/course/chapter/list/123
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "id": 1001,
        "courseId": 123,
        "title": "SWOT分析法基础",
        "sortOrder": 1,
        "createTime": "2024-01-15 10:30:00",
        "lessons": [
          {
            "id": 2001,
            "chapterId": 1001,
            "title": "课时1：SWOT分析法概述",
            "lessonType": "视频",
            "isFree": true,
            "materialId": "material001",
            "materialName": "课程导论.mp4",
            "materialFileUrl": "https://example.com/video1.mp4",
            "sortOrder": 1,
            "createTime": "2024-01-15 10:30:00"
          },
          {
            "id": 2002,
            "chapterId": 1001,
            "title": "课时2：SWOT分析实战演练",
            "lessonType": "视频",
            "isFree": false,
            "materialId": "material002",
            "materialName": "SWOT实战演练.mp4",
            "materialFileUrl": "https://example.com/video2.mp4",
            "sortOrder": 2,
            "createTime": "2024-01-15 10:35:00"
          }
        ]
      },
      {
        "id": 1002,
        "courseId": 123,
        "title": "SWOT分析法实践",
        "sortOrder": 2,
        "createTime": "2024-01-15 10:35:00",
        "lessons": [
          {
            "id": 2003,
            "chapterId": 1002,
            "title": "课时3：案例分析",
            "lessonType": "文档",
            "isFree": false,
            "materialId": "material003",
            "materialName": "案例分析.pdf",
            "materialFileUrl": "https://example.com/doc1.pdf",
            "sortOrder": 1,
            "createTime": "2024-01-15 10:40:00"
          }
        ]
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 3.2 新增课程章节

**接口地址**: `POST /chapter/add`

**功能说明**: 为指定课程新增章节

**请求参数**:

| 参数名    | 类型    | 必填 | 说明                  |
| --------- | ------- | ---- | --------------------- |
| courseId  | Long    | 是   | 课程ID                |
| title     | String  | 是   | 章节标题，最大200字符 |
| sortOrder | Integer | 否   | 排序序号，默认为0     |

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| id     | Long | 新增章节的ID |

**请求示例**:

```json
POST /publicbiz/digital-asset/course/chapter/add
Content-Type: application/json

{
  "courseId": 123,
  "title": "SWOT分析法进阶",
  "sortOrder": 3
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1003
  },
  "timestamp": 1640995200000
}
```

### 3.3 更新课程章节

**接口地址**: `PUT /chapter/update`

**功能说明**: 更新课程章节信息

**请求参数**:

| 参数名    | 类型    | 必填 | 说明                  |
| --------- | ------- | ---- | --------------------- |
| id        | Long    | 是   | 章节ID                |
| title     | String  | 是   | 章节标题，最大200字符 |
| sortOrder | Integer | 否   | 排序序号              |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```json
PUT /publicbiz/digital-asset/course/chapter/update
Content-Type: application/json

{
  "id": 1003,
  "title": "SWOT分析法高级应用",
  "sortOrder": 3
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

### 3.4 删除课程章节

**接口地址**: `DELETE /chapter/delete/{id}`

**功能说明**: 删除指定章节（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 章节ID（路径参数） |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```http
DELETE /publicbiz/digital-asset/course/chapter/delete/1003
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

## 4. 课程课时管理接口（线上课程专用）

### 4.1 新增课程课时

**接口地址**: `POST /lesson/add`

**功能说明**: 为指定章节新增课时

**请求参数**:

| 参数名          | 类型    | 必填 | 说明                       |
| --------------- | ------- | ---- | -------------------------- |
| courseId        | Long    | 是   | 课程ID                     |
| chapterId       | Long    | 是   | 章节ID                     |
| title           | String  | 是   | 课时标题，最大200字符      |
| lessonType      | String  | 是   | 课时类型：视频、文档、音频 |
| isFree          | Boolean | 否   | 是否免费试看，默认false    |
| materialId      | String  | 否   | 关联素材ID                 |
| materialName    | String  | 是   | 关联素材名称               |
| materialFileUrl | String  | 否   | 关联素材文件URL            |
| sortOrder       | Integer | 否   | 排序序号，默认为0          |

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| id     | Long | 新增课时的ID |

**请求示例**:

```json
POST /publicbiz/digital-asset/course/lesson/add
Content-Type: application/json

{
  "courseId": 123,
  "chapterId": 1001,
  "title": "课时2：SWOT分析实战演练",
  "lessonType": "视频",
  "isFree": false,
  "materialId": "material002",
  "materialName": "SWOT实战演练.mp4",
  "materialFileUrl": "https://example.com/video2.mp4",
  "sortOrder": 2
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 2002
  },
  "timestamp": 1640995200000
}
```

### 4.2 更新课程课时

**接口地址**: `PUT /lesson/update`

**功能说明**: 更新课程课时信息

**请求参数**:

| 参数名          | 类型    | 必填 | 说明                       |
| --------------- | ------- | ---- | -------------------------- |
| id              | Long    | 是   | 课时ID                     |
| courseId        | Long    | 是   | 课程ID                     |
| chapterId       | Long    | 是   | 章节ID                     |
| title           | String  | 是   | 课时标题，最大200字符      |
| lessonType      | String  | 是   | 课时类型：视频、文档、音频 |
| isFree          | Boolean | 否   | 是否免费试看，默认false    |
| materialId      | String  | 否   | 关联素材ID                 |
| materialName    | String  | 是   | 关联素材名称               |
| materialFileUrl | String  | 否   | 关联素材文件URL            |
| sortOrder       | Integer | 否   | 排序序号                   |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```json
PUT /publicbiz/digital-asset/course/lesson/update
Content-Type: application/json

{
  "id": 2002,
  "courseId": 123,
  "chapterId": 1001,
  "title": "课时2：SWOT分析实战演练（更新版）",
  "lessonType": "视频",
  "isFree": false,
  "materialId": "material002",
  "materialName": "SWOT实战演练_v2.mp4",
  "materialFileUrl": "https://example.com/video2_v2.mp4",
  "sortOrder": 2
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```


### 4.3 删除课程课时

**接口地址**: `DELETE /lesson/delete/{id}`

**功能说明**: 删除指定的课程课时

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 课时ID（路径参数） |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```http
DELETE /publicbiz/digital-asset/course/lesson/delete/2002
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

**注意事项**:

- 删除课时后无法恢复，请谨慎操作
- 删除课时不会影响其他课时的排序序号
- 如果课时关联了素材文件，删除课时不会删除素材文件本身

## 5. 课程附件管理接口

### 5.1 获取课程附件列表

**接口地址**: `GET /attachment/list/{courseId}`

**功能说明**: 获取指定课程的附件列表

**请求参数**:

| 参数名   | 类型 | 必填 | 说明               |
| -------- | ---- | ---- | ------------------ |
| courseId | Long | 是   | 课程ID（路径参数） |

**响应字段**:

| 字段名                | 类型   | 说明                       |
| --------------------- | ------ | -------------------------- |
| list                  | Array  | 附件列表                   |
| list[].id             | Long   | 附件ID                     |
| list[].courseId       | Long   | 课程ID                     |
| list[].attachmentName | String | 附件名称                   |
| list[].attachmentType | String | 附件类型：视频、文档、音频 |
| list[].fileUrl        | String | 文件URL                    |
| list[].fileSize       | Long   | 文件大小（字节）           |
| list[].createTime     | String | 创建时间                   |

**请求示例**:

```http
GET /publicbiz/digital-asset/course/attachment/list/123
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "id": 3001,
        "courseId": 123,
        "attachmentName": "开课须知.md",
        "attachmentType": "文档",
        "fileUrl": "https://example.com/notice.md",
        "fileSize": 2048,
        "createTime": "2024-01-15 10:30:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 5.2 添加课程附件

**接口地址**: `POST /attachment/add`

**功能说明**: 为指定课程添加附件

**请求参数**:

| 参数名         | 类型   | 必填 | 说明                       |
| -------------- | ------ | ---- | -------------------------- |
| courseId       | Long   | 是   | 课程ID                     |
| attachmentName | String | 是   | 附件名称，最大200字符      |
| attachmentType | String | 是   | 附件类型：视频、文档、音频 |
| fileUrl        | String | 是   | 文件URL                    |
| fileSize       | Long   | 否   | 文件大小（字节）           |

**响应字段**:

| 字段名 | 类型 | 说明         |
| ------ | ---- | ------------ |
| id     | Long | 新增附件的ID |

**请求示例**:

```json
POST /publicbiz/digital-asset/course/attachment/add
Content-Type: application/json

{
  "courseId": 123,
  "attachmentName": "课程学习指南.pdf",
  "attachmentType": "文档",
  "fileUrl": "https://example.com/guide.pdf",
  "fileSize": 1024000
}
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 3002
  },
  "timestamp": 1640995200000
}
```

### 5.3 移除课程附件

**接口地址**: `DELETE /attachment/remove/{id}`

**功能说明**: 移除指定的课程附件

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 附件ID（路径参数） |

**响应字段**: 无特殊数据，仅返回操作结果

**请求示例**:

```http
DELETE /publicbiz/digital-asset/course/attachment/remove/3002
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null,
  "timestamp": 1640995200000
}
```

## 6. 统计报表接口

### 6.1 课程统计概览

**接口地址**: `GET /statistics/overview`

**功能说明**: 获取课程统计概览数据（课程总数、线上课程数、线下课程数、已上架数）

**请求参数**: 无

**响应字段**:

| 字段名         | 类型    | 说明         |
| -------------- | ------- | ------------ |
| totalCount     | Integer | 课程总数     |
| onlineCount    | Integer | 线上课程数   |
| offlineCount   | Integer | 线下课程数   |
| publishedCount | Integer | 已上架课程数 |

**请求示例**:

```http
GET /publicbiz/digital-asset/course/statistics/overview
```

**返回示例**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "totalCount": 25,
    "onlineCount": 15,
    "offlineCount": 10,
    "publishedCount": 18
  },
  "timestamp": 1640995200000
}
```

## 7. 数据字典

### 7.1 课程分类枚举

| 值       | 说明                 |
| -------- | -------------------- |
| 家政技能 | 家政服务相关技能培训 |
| 职业素养 | 职业发展和素养提升   |
| 高校实践 | 高校学生实践课程     |
| 企业管理 | 企业管理相关课程     |

### 7.2 课程状态枚举

| 值     | 说明                 |
| ------ | -------------------- |
| 待发布 | 课程已创建但未发布   |
| 已上架 | 课程已发布并可供学习 |
| 已下架 | 课程已下架，暂停学习 |

### 7.3 授课方式枚举

| 值       | 说明         |
| -------- | ------------ |
| 线上授课 | 在线学习课程 |
| 线下授课 | 线下面授课程 |

### 7.4 业务板块枚举

| 值       | 说明         |
| -------- | ------------ |
| 家政服务 | 家政服务业务 |
| 高校实践 | 高校实践业务 |
| 培训管理 | 培训管理业务 |
| 就业服务 | 就业服务业务 |
| 兼职零工 | 兼职零工业务 |

### 7.5 课时类型枚举

| 值   | 说明     |
| ---- | -------- |
| 视频 | 视频课时 |
| 文档 | 文档课时 |
| 音频 | 音频课时 |

### 7.6 附件类型枚举

| 值   | 说明     |
| ---- | -------- |
| 视频 | 视频附件 |
| 文档 | 文档附件 |
| 音频 | 音频附件 |

## 8. 错误码说明

### 8.1 通用错误码

| 错误码 | 错误信息       | 说明                     |
| ------ | -------------- | ------------------------ |
| 0      | 操作成功       | 请求处理成功             |
| 400    | 请求参数错误   | 请求参数格式或内容错误   |
| 401    | 未授权访问     | 用户未登录或token无效    |
| 403    | 权限不足       | 用户无权限访问该资源     |
| 404    | 资源不存在     | 请求的资源不存在         |
| 500    | 服务器内部错误 | 服务器处理请求时发生错误 |

### 8.2 业务错误码

| 错误码 | 错误信息                    | 说明                             |
| ------ | --------------------------- | -------------------------------- |
| 10001  | 课程名称不能为空            | 课程名称为必填字段               |
| 10002  | 课程名称长度不能超过200字符 | 课程名称长度限制                 |
| 10003  | 授课方式不能为空            | 授课方式为必填字段               |
| 10004  | 课程分类不能为空            | 课程分类为必填字段               |
| 10005  | 课程状态不能为空            | 课程状态为必填字段               |
| 10006  | 课程不存在                  | 指定的课程ID不存在               |
| 10007  | 课程状态无效                | 课程状态参数值无效               |
| 10008  | 章节不存在                  | 指定的章节ID不存在               |
| 10009  | 课时不存在                  | 指定的课时ID不存在               |
| 10010  | 附件不存在                  | 指定的附件ID不存在               |
| 10011  | 线下课程不支持章节课时管理  | 线下课程无法进行章节课时操作     |
| 10012  | 课程状态不允许修改          | 当前课程状态下不允许进行修改操作 |
| 10013  | 附件名称不能为空            | 附件名称为必填字段               |
| 10014  | 附件类型不能为空            | 附件类型为必填字段               |
| 10015  | 文件URL不能为空             | 文件URL为必填字段                |

## 9. 接口调用说明

### 9.1 认证方式

所有接口都需要在请求头中携带认证信息：

```http
Authorization: Bearer {token}
```

### 9.2 请求限制

- 单次请求超时时间：30秒
- 分页查询最大页面大小：100
- 文件上传最大大小：100MB

### 9.3 注意事项

1. 所有时间字段格式为：`yyyy-MM-dd HH:mm:ss`
2. 文件URL需要是完整的可访问地址
3. 线下课程不支持章节课时管理功能
4. 课程状态变更需要满足业务规则约束
