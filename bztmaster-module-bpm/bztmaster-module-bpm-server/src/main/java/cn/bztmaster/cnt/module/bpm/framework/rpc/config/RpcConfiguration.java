package cn.bztmaster.cnt.module.bpm.framework.rpc.config;

import cn.bztmaster.cnt.module.system.api.dept.DeptApi;
import cn.bztmaster.cnt.module.system.api.dept.PostApi;
import cn.bztmaster.cnt.module.system.api.dict.DictDataApi;
import cn.bztmaster.cnt.module.system.api.permission.PermissionApi;
import cn.bztmaster.cnt.module.system.api.permission.RoleApi;
import cn.bztmaster.cnt.module.system.api.sms.SmsSendApi;
import cn.bztmaster.cnt.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(value = "bpmRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {RoleApi.class, DeptApi.class, PostApi.class, AdminUserApi.class, SmsSendApi.class, DictDataApi.class,
        PermissionApi.class})
public class RpcConfiguration {
}
