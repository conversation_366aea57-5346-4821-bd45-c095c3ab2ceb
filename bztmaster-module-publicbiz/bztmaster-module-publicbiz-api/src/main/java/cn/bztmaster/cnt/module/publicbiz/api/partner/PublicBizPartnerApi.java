package cn.bztmaster.cnt.module.publicbiz.api.partner;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 合作伙伴中心")
public interface PublicBizPartnerApi {

    String PREFIX = "/publicbiz/partner";

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "新增合作伙伴")
    CommonResult<Long> createPartner(@RequestBody PartnerSaveReqDTO reqDTO);

    @PostMapping(PREFIX + "/update")
    @Operation(summary = "编辑合作伙伴")
    CommonResult<Boolean> updatePartner(@RequestBody PartnerUpdateReqDTO reqDTO);

    @PostMapping(PREFIX + "/delete")
    @Operation(summary = "删除合作伙伴")
    CommonResult<Boolean> deletePartner(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/detail")
    @Operation(summary = "合作伙伴详情")
    CommonResult<PartnerRespDTO> getPartnerDetail(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/page")
    @Operation(summary = "合作伙伴分页查询")
    CommonResult<PartnerPageRespDTO> getPartnerPage(PartnerPageReqDTO reqDTO);

    @GetMapping(PREFIX + "/stat")
    @Operation(summary = "合作伙伴统计卡片数据")
    CommonResult<PartnerStatRespDTO> getPartnerStat();
}
