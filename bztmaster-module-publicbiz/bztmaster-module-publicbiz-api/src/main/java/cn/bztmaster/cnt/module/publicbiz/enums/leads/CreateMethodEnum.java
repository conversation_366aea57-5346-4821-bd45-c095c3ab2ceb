package cn.bztmaster.cnt.module.publicbiz.enums.leads;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 创建方式枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CreateMethodEnum {

    MANUAL_CREATION(1, "手动录入"),
    SYSTEM_IMPORT(2, "系统生成");

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static CreateMethodEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (CreateMethodEnum value : CreateMethodEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}