package cn.bztmaster.cnt.module.publicbiz.api.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.business.dto.BusinessRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.*;

@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 商机中心")
public interface PublicBizBusinessApi {

    String PREFIX = "/publicbiz/business";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过商机ID查询商机")
    @Parameter(name = "id", description = "商机编号", example = "1", required = true)
    CommonResult<BusinessRespDTO> getBusiness(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过商机ID查询商机们")
    @Parameter(name = "ids", description = "商机编号数组", example = "1,2", required = true)
    CommonResult<List<BusinessRespDTO>> getBusinessList(@RequestParam("ids") Collection<Long> ids);

    // 可根据业务需求继续补充接口
} 