package cn.bztmaster.cnt.module.publicbiz.enums.certificateTemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 证书模板状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CertificateTemplateStatusEnum {

    DRAFT(1, "draft", "草稿"),
    ACTIVE(2, "active", "启用中"),
    INACTIVE(3, "inactive", "已停用");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CertificateTemplateStatusEnum::getStatus).toArray();

    /**
     * 状态编号
     */
    private final Integer status;

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 获取所有状态编号数组
     *
     * @return 状态编号数组
     */
    public static int[] getStatusArray() {
        return ARRAYS;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 模板状态枚举
     */
    public static CertificateTemplateStatusEnum getByCode(String code) {
        for (CertificateTemplateStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 根据状态编号获取枚举
     *
     * @param status 状态编号
     * @return 模板状态枚举
     */
    public static CertificateTemplateStatusEnum getByStatus(Integer status) {
        for (CertificateTemplateStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}