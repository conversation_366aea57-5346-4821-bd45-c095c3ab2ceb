package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplatePageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateListReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateStatisticsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 证书模板 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 证书模板")
public interface CertificateTemplateApi {

    String PREFIX = ApiConstants.PREFIX + "/certificate/template";

    @GetMapping(PREFIX + "/page")
    @Operation(summary = "分页查询证书模板")
    CommonResult<PageResult<CertificateTemplateRespDTO>> pageCertificateTemplate(@Valid CertificateTemplatePageReqDTO pageReqDTO);

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得证书模板详情")
    @Parameter(name = "id", description = "模板编号", example = "1024", required = true)
    CommonResult<CertificateTemplateRespDTO> getCertificateTemplate(@RequestParam("id") Long id);

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建证书模板")
    CommonResult<Long> createCertificateTemplate(@Valid @RequestBody CertificateTemplateSaveReqDTO createReqDTO);

    @PostMapping(PREFIX + "/update")
    @Operation(summary = "更新证书模板")
    CommonResult<Boolean> updateCertificateTemplate(@Valid @RequestBody CertificateTemplateSaveReqDTO updateReqDTO);

    @PostMapping(PREFIX + "/delete")
    @Operation(summary = "删除证书模板")
    @Parameter(name = "id", description = "模板编号", example = "1024", required = true)
    CommonResult<Boolean> deleteCertificateTemplate(@RequestParam("id") Long id);

    @PostMapping(PREFIX + "/update-status")
    @Operation(summary = "更新证书模板状态")
    CommonResult<Boolean> updateCertificateTemplateStatus(@RequestParam("id") Long id, @RequestParam("status") String status);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得证书模板列表")
    CommonResult<List<CertificateTemplateRespDTO>> listCertificateTemplate(@Valid CertificateTemplateListReqDTO listReqDTO);

    @GetMapping(PREFIX + "/statistics")
    @Operation(summary = "获得证书模板统计")
    CommonResult<CertificateTemplateStatisticsRespDTO> getCertificateTemplateStatistics();

}