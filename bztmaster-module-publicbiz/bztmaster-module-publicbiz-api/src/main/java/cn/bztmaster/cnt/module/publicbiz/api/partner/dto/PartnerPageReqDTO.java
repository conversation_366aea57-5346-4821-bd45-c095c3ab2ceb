package cn.bztmaster.cnt.module.publicbiz.api.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "合作伙伴 - 分页查询 Request DTO")
public class PartnerPageReqDTO {
    @Schema(description = "页码", example = "1")
    private Integer page;

    @Schema(description = "每页数量", example = "10")
    private Integer size;

    @Schema(description = "机构类型")
    private String type;

    @Schema(description = "业务模块")
    private String biz;

    @Schema(description = "合作状态")
    private String status;

    @Schema(description = "关键字（模糊匹配机构名称和负责人姓名）")
    private String keyword;

    @Schema(description = "我方负责人ID")
    private Long owner;
}