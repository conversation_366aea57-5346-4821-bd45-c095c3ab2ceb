package cn.bztmaster.cnt.module.publicbiz.api.leads.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 线索信息列表查询 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "线索信息列表查询请求 DTO")
@Data
public class LeadsListReqDTO {

    @Schema(description = "客户姓名，模糊匹配", example = "张")
    private String customerName;

    @Schema(description = "联系电话，模糊匹配", example = "138")
    private String customerPhone;

    @Schema(description = "线索来源", example = "1")
    private Integer leadSource;

    @Schema(description = "业务模块", example = "1")
    private Integer businessModule;

    @Schema(description = "线索状态", example = "1")
    private Integer leadStatus;

    @Schema(description = "创建方式", example = "1")
    private Integer createMethod;

    @Schema(description = "当前跟进人", example = "李四")
    private String currentOwner;
}