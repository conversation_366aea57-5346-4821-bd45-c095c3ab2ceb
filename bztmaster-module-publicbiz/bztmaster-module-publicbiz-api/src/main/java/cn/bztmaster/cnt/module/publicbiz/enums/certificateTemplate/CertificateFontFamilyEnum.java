package cn.bztmaster.cnt.module.publicbiz.enums.certificateTemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 证书字体族枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CertificateFontFamilyEnum {

    MICROSOFT_YAHEI(1, "微软雅黑", "微软雅黑"),
    HEITI(2, "黑体", "黑体"),
    ARIAL(3, "Arial", "Arial"),
    TIMES_NEW_ROMAN(4, "Times New Roman", "Times New Roman"),
    SONGTI(5, "宋体", "宋体");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CertificateFontFamilyEnum::getType).toArray();

    /**
     * 类型编号
     */
    private final Integer type;

    /**
     * 字体族代码
     */
    private final String code;

    /**
     * 字体族名称
     */
    private final String name;

    /**
     * 获取所有类型编号数组
     *
     * @return 类型编号数组
     */
    public static int[] getTypeArray() {
        return ARRAYS;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 字体族代码
     * @return 字体族枚举
     */
    public static CertificateFontFamilyEnum getByCode(String code) {
        for (CertificateFontFamilyEnum fontEnum : values()) {
            if (fontEnum.getCode().equals(code)) {
                return fontEnum;
            }
        }
        return null;
    }

    /**
     * 根据类型编号获取枚举
     *
     * @param type 类型编号
     * @return 字体族枚举
     */
    public static CertificateFontFamilyEnum getByType(Integer type) {
        for (CertificateFontFamilyEnum fontEnum : values()) {
            if (fontEnum.getType().equals(type)) {
                return fontEnum;
            }
        }
        return null;
    }
}