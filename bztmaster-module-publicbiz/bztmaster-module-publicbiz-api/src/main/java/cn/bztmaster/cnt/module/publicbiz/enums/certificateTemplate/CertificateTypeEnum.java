package cn.bztmaster.cnt.module.publicbiz.enums.certificateTemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 证书类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CertificateTypeEnum {

    TRAINING(1, "training", "培训证书"),
    COMPLETION(2, "completion", "结业证书"),
    SKILL(3, "skill", "技能证书");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CertificateTypeEnum::getType).toArray();

    /**
     * 类型编号
     */
    private final Integer type;

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 获取所有类型编号数组
     *
     * @return 类型编号数组
     */
    public static int[] getTypeArray() {
        return ARRAYS;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 证书类型枚举
     */
    public static CertificateTypeEnum getByCode(String code) {
        for (CertificateTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据类型编号获取枚举
     *
     * @param type 类型编号
     * @return 证书类型枚举
     */
    public static CertificateTypeEnum getByType(Integer type) {
        for (CertificateTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}