package cn.bztmaster.cnt.module.publicbiz.api.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.business.dto.BusinessLogRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.*;

@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 商机日志")
public interface PublicBizBusinessLogApi {

    String PREFIX = "/publicbiz/business-log";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过日志ID查询商机日志")
    @Parameter(name = "id", description = "日志编号", example = "1", required = true)
    CommonResult<BusinessLogRespDTO> getBusinessLog(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过日志ID查询商机日志们")
    @Parameter(name = "ids", description = "日志编号数组", example = "1,2", required = true)
    CommonResult<List<BusinessLogRespDTO>> getBusinessLogList(@RequestParam("ids") Collection<Long> ids);

    // 可根据业务需求继续补充接口
} 