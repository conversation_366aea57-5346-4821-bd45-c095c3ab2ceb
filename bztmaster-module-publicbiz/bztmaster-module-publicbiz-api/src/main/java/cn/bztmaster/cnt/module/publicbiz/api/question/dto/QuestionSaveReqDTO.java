package cn.bztmaster.cnt.module.publicbiz.api.question.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考题保存 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class QuestionSaveReqDTO {

    /**
     * 考题ID（编辑时必填）
     */
    private Long id;

    /**
     * 一级分类名称
     */
    private String level1Name;

    /**
     * 一级分类代码
     */
    private String level1Code;

    /**
     * 二级分类名称
     */
    private String level2Name;

    /**
     * 二级分类代码
     */
    private String level2Code;

    /**
     * 三级分类名称
     */
    private String level3Name;

    /**
     * 三级分类代码
     */
    private String level3Code;

    /**
     * 认定点名称
     */
    private String certName;

    /**
     * 认定点代码
     */
    private String certCode;

    /**
     * 题干内容
     */
    private String title;

    /**
     * 题型
     */
    private String type;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 业务模块
     */
    private String biz;

    /**
     * 业务模块名称
     */
    private String bizName;

    /**
     * 难度等级：1-简单，2-中等，3-困难
     */
    private Integer difficulty;

    /**
     * 题目分值
     */
    private BigDecimal score;

    /**
     * 答题时间限制（秒）
     */
    private Integer timeLimit;

    /**
     * 题目解析
     */
    private String explanation;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 选项列表
     */
    private List<QuestionOptionDTO> options;

}
