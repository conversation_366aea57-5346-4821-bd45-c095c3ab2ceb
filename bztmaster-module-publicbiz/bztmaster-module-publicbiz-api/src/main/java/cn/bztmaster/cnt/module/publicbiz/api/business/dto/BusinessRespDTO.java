package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机 Response DTO")
public class BusinessRespDTO implements VO {
    @Schema(description = "商机ID", example = "1")
    private Long id;

    @Schema(description = "商机名称", example = "示例商机")
    private String name;

    @Schema(description = "客户ID", example = "1001")
    private Long customerId;

    @Schema(description = "客户名称", example = "示例客户")
    private String customerName;

    @Schema(description = "商机状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "最后更新时间")
    private Date updateTime;

    @Schema(description = "负责人ID")
    private Long ownerId;

    @Schema(description = "负责人名称")
    private String ownerName;

    @Schema(description = "预计成交日期")
    private Date expectedDealDate;

    @Schema(description = "商机描述")
    private String description;
} 