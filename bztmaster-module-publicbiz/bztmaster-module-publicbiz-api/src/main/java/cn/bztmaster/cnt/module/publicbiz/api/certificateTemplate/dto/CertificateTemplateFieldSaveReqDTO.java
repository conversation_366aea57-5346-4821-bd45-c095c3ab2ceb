package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 证书模板字段创建/更新 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 证书模板字段创建/更新 Request DTO")
@Data
public class CertificateTemplateFieldSaveReqDTO {

    @Schema(description = "字段唯一标识符", requiredMode = Schema.RequiredMode.REQUIRED, example = "field_1")
    @NotBlank(message = "字段唯一标识符不能为空")
    @Size(max = 50, message = "字段唯一标识符长度不能超过50个字符")
    private String fieldId;

    @Schema(description = "字段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    @NotBlank(message = "字段类型不能为空")
    private String fieldType;

    @Schema(description = "字段标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "【学员姓名】")
    @NotBlank(message = "字段标签不能为空")
    @Size(max = 100, message = "字段标签长度不能超过100个字符")
    private String fieldLabel;

    @Schema(description = "X坐标位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "350")
    @NotNull(message = "X坐标位置不能为空")
    private Integer positionX;

    @Schema(description = "Y坐标位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    @NotNull(message = "Y坐标位置不能为空")
    private Integer positionY;

    @Schema(description = "字体大小", requiredMode = Schema.RequiredMode.REQUIRED, example = "32")
    @NotNull(message = "字体大小不能为空")
    private Integer fontSize;

    @Schema(description = "字体颜色", requiredMode = Schema.RequiredMode.REQUIRED, example = "#333")
    @NotBlank(message = "字体颜色不能为空")
    @Size(max = 20, message = "字体颜色长度不能超过20个字符")
    private String fontColor;

    @Schema(description = "字体族", requiredMode = Schema.RequiredMode.REQUIRED, example = "微软雅黑")
    @NotBlank(message = "字体族不能为空")
    @Size(max = 50, message = "字体族长度不能超过50个字符")
    private String fontFamily;

    @Schema(description = "排序顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "排序顺序不能为空")
    private Integer sortOrder;

}