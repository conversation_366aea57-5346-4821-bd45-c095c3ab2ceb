package cn.bztmaster.cnt.module.publicbiz.enums.certificateTemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 证书字段类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CertificateFieldTypeEnum {

    NAME(1, "name", "学员姓名"),
    CODE(2, "code", "证书编号"),
    ID(3, "id", "身份证号"),
    DATE(4, "date", "发证日期"),
    QRCODE(5, "qrcode", "证书查验二维码");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CertificateFieldTypeEnum::getType).toArray();

    /**
     * 类型编号
     */
    private final Integer type;

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 获取所有类型编号数组
     *
     * @return 类型编号数组
     */
    public static int[] getTypeArray() {
        return ARRAYS;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 字段类型枚举
     */
    public static CertificateFieldTypeEnum getByCode(String code) {
        for (CertificateFieldTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据类型编号获取枚举
     *
     * @param type 类型编号
     * @return 字段类型枚举
     */
    public static CertificateFieldTypeEnum getByType(Integer type) {
        for (CertificateFieldTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}