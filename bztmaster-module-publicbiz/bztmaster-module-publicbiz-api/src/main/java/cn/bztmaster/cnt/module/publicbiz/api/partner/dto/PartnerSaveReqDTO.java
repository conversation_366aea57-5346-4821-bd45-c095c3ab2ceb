package cn.bztmaster.cnt.module.publicbiz.api.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "合作伙伴 - 新增 Request DTO")
public class PartnerSaveReqDTO {
    @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机构名称不能为空")
    @Size(max = 128, message = "机构名称长度不能超过128字符")
    private String name;

    @Schema(description = "机构类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机构类型不能为空")
    @Size(max = 32, message = "机构类型长度不能超过32字符")
    private String type;

    @Schema(description = "业务模块", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "业务模块不能为空")
    @Size(max = 32, message = "业务模块长度不能超过32字符")
    private String biz;

    @Schema(description = "合作状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "合作状态不能为空")
    @Size(max = 32, message = "合作状态长度不能超过32字符")
    private String status;

    @Schema(description = "风险等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "风险等级不能为空")
    @Size(max = 16, message = "风险等级长度不能超过16字符")
    private String risk;

    @Schema(description = "我方负责人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "我方负责人ID不能为空")
    private Long owner;
}
