package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 附件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AttachmentTypeEnum {

    VIDEO("视频", "视频附件"),
    DOCUMENT("文档", "文档附件"),
    AUDIO("音频", "音频附件");

    /**
     * 类型值
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 枚举
     */
    public static AttachmentTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (AttachmentTypeEnum value : AttachmentTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
