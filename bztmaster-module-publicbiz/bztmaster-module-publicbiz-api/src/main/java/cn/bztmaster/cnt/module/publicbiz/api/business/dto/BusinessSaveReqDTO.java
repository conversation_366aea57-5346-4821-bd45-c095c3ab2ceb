package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "商机中心 - 商机新增/更新 Request DTO")
public class BusinessSaveReqDTO {
    @Schema(description = "商机ID", example = "1")
    private Long id;

    @Schema(description = "商机名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商机名称不能为空")
    private String name;

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    @Schema(description = "商机状态")
    private Integer status;

    @Schema(description = "负责人ID")
    private Long ownerId;

    @Schema(description = "负责人名称")
    private String ownerName;

    @Schema(description = "预计成交日期")
    private java.util.Date expectedDealDate;

    @Schema(description = "商机描述")
    private String description;
} 