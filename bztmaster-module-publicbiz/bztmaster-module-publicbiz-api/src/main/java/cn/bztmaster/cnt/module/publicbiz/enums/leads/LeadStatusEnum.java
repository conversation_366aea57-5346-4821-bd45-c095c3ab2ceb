package cn.bztmaster.cnt.module.publicbiz.enums.leads;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线索状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LeadStatusEnum {

    UNPROCESSED(1, "未处理"),
    FOLLOWING_UP(2, "跟进中"),
    CONVERTED(3, "已转化"),
    NO_INTENTION(4, "无意向");

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static LeadStatusEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (LeadStatusEnum value : LeadStatusEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}