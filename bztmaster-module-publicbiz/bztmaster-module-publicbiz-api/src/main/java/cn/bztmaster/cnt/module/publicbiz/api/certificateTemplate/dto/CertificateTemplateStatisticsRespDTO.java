package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 证书模板统计 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 证书模板统计 Response DTO")
@Data
public class CertificateTemplateStatisticsRespDTO {

    @Schema(description = "证书模板总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "15")
    private Integer total;

    @Schema(description = "启用中的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    private Integer activeCount;

    @Schema(description = "已停用的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    private Integer inactiveCount;

    @Schema(description = "草稿状态的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer draftCount;

}