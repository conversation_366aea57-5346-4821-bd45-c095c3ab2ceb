package cn.bztmaster.cnt.module.publicbiz.api.question;

import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionSaveReqDTO;

import java.util.Collection;
import java.util.List;

/**
 * 考题 API 接口
 *
 * <AUTHOR>
 */
public interface QuestionApi {

    /**
     * 根据ID查询考题
     *
     * @param id 考题ID
     * @return 考题信息
     */
    QuestionRespDTO getQuestion(Long id);

    /**
     * 根据ID列表查询考题列表
     *
     * @param ids 考题ID列表
     * @return 考题列表
     */
    List<QuestionRespDTO> getQuestionList(Collection<Long> ids);

    /**
     * 分页查询考题
     *
     * @param reqDTO 查询条件
     * @return 考题列表
     */
    List<QuestionRespDTO> getQuestionPage(QuestionPageReqDTO reqDTO);

    /**
     * 创建考题
     *
     * @param reqDTO 考题信息
     * @return 考题ID
     */
    Long createQuestion(QuestionSaveReqDTO reqDTO);

    /**
     * 更新考题
     *
     * @param reqDTO 考题信息
     */
    void updateQuestion(QuestionSaveReqDTO reqDTO);

    /**
     * 删除考题
     *
     * @param id 考题ID
     */
    void deleteQuestion(Long id);

    /**
     * 校验考题是否存在
     *
     * @param id 考题ID
     */
    void validateQuestionExists(Long id);

}
