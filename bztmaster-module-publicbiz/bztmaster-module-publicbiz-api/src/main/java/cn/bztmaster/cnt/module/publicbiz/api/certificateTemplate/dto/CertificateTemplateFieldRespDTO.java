package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 证书模板字段 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 证书模板字段 Response DTO")
@Data
public class CertificateTemplateFieldRespDTO {

    @Schema(description = "字段ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "字段唯一标识符", requiredMode = Schema.RequiredMode.REQUIRED, example = "field_1")
    private String fieldId;

    @Schema(description = "字段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "name")
    private String fieldType;

    @Schema(description = "字段标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "【学员姓名】")
    private String fieldLabel;

    @Schema(description = "X坐标位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "350")
    private Integer positionX;

    @Schema(description = "Y坐标位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    private Integer positionY;

    @Schema(description = "字体大小", requiredMode = Schema.RequiredMode.REQUIRED, example = "32")
    private Integer fontSize;

    @Schema(description = "字体颜色", requiredMode = Schema.RequiredMode.REQUIRED, example = "#333")
    private String fontColor;

    @Schema(description = "字体族", requiredMode = Schema.RequiredMode.REQUIRED, example = "微软雅黑")
    private String fontFamily;

    @Schema(description = "排序顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sortOrder;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建人姓名", example = "管理员")
    private String creatorName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    private LocalDateTime updateTime;

}