package cn.bztmaster.cnt.module.publicbiz.api.leads.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 线索跟进记录保存 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "线索跟进记录保存请求 DTO")
@Data
public class LeadFollowUpLogSaveReqDTO {

    @Schema(description = "主键，更新时必传", example = "1024")
    private Long id;

    @Schema(description = "关联的线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    @NotEmpty(message = "线索ID不能为空")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    @Schema(description = "跟进内容详情", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户对我们的产品表示很感兴趣，计划下周再次沟通")
    @NotEmpty(message = "跟进内容不能为空")
    private String followUpContent;
}