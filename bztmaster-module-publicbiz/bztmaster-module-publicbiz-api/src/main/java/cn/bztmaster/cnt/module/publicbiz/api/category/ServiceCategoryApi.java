package cn.bztmaster.cnt.module.publicbiz.api.category;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 服务分类")
public interface ServiceCategoryApi {

    String PREFIX = ApiConstants.PREFIX + "/category";

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验服务分类是否合法")
    @Parameter(name = "ids", description = "服务分类编号数组", example = "1,2", required = true)
    CommonResult<Boolean> validateCategoryList(@RequestParam("ids") Collection<Long> ids);

} 