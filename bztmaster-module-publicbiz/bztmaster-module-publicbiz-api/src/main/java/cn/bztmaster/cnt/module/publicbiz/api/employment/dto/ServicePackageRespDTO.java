package cn.bztmaster.cnt.module.publicbiz.api.employment.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务套餐 Response DTO
 */
@Data
public class ServicePackageRespDTO {
    /**
     * 套餐ID
     */
    private Long id;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 服务分类
     */
    private String category;

    /**
     * 套餐主图URL
     */
    private String thumbnail;

    /**
     * 套餐价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 价格单位
     */
    private String unit;

    /**
     * 服务时长
     */
    private String serviceDuration;

    /**
     * 套餐类型
     */
    private String packageType;

    /**
     * 任务拆分规则
     */
    private String taskSplitRule;

    /**
     * 服务描述
     */
    private String serviceDescription;

    /**
     * 详细服务内容
     */
    private String serviceDetails;

    /**
     * 服务流程
     */
    private String serviceProcess;

    /**
     * 购买须知
     */
    private String purchaseNotice;

    /**
     * 状态
     */
    private String status;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 所属机构ID
     */
    private Long agencyId;

    /**
     * 所属机构名称
     */
    private String agencyName;

    /**
     * 预约时间范围
     */
    private Integer advanceBookingDays;

    /**
     * 时间选择模式
     */
    private String timeSelectionMode;

    /**
     * 预约模式
     */
    private String appointmentMode;

    /**
     * 服务开始时间
     */
    private String serviceStartTime;

    /**
     * 地址设置
     */
    private String addressSetting;

    /**
     * 最大预约天数
     */
    private Integer maxBookingDays;

    /**
     * 取消政策
     */
    private String cancellationPolicy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}