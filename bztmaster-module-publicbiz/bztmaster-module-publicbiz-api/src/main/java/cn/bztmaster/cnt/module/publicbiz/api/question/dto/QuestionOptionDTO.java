package cn.bztmaster.cnt.module.publicbiz.api.question.dto;

import lombok.Data;

/**
 * 考题选项 DTO
 *
 * <AUTHOR>
 */
@Data
public class QuestionOptionDTO {

    /**
     * 选项标识：A、B、C、D等
     */
    private String optionKey;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 是否正确答案
     */
    private Boolean isCorrect;

    /**
     * 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列
     */
    private String optionType;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 匹配目标，用于匹配题记录对应关系
     */
    private String matchTarget;

}
