package cn.bztmaster.cnt.module.publicbiz.api.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.digitalAsset.dto.DigitalAssetCourseRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.*;

/**
 * 数字资产课程 API 接口
 * 
 * <AUTHOR>
 */
@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 数字资产课程")
public interface DigitalAssetCourseApi {

    String PREFIX = "/publicbiz/digital-asset/course";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过课程ID查询课程")
    @Parameter(name = "id", description = "课程编号", example = "1", required = true)
    CommonResult<DigitalAssetCourseRespDTO> getCourse(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过课程ID查询课程们")
    @Parameter(name = "ids", description = "课程编号数组", example = "1,2", required = true)
    CommonResult<List<DigitalAssetCourseRespDTO>> getCourseList(@RequestParam("ids") Collection<Long> ids);

    // 可根据业务需求继续补充接口
}
