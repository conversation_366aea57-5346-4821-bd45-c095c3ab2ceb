package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数字资产课程状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CourseStatusEnum {

    PENDING("待发布", "课程已创建但未发布"),
    PUBLISHED("已上架", "课程已发布并可供学习"),
    OFFLINE("已下架", "课程已下架，暂停学习");

    /**
     * 状态值
     */
    private final String status;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static CourseStatusEnum getByStatus(String status) {
        if (status == null) {
            return null;
        }
        for (CourseStatusEnum value : CourseStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}
