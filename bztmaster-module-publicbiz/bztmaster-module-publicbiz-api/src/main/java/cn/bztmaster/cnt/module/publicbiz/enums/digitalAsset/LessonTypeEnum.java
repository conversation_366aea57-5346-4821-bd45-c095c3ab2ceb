package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 课时类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LessonTypeEnum {

    VIDEO("视频", "视频课时"),
    DOCUMENT("文档", "文档课时"),
    AUDIO("音频", "音频课时");

    /**
     * 类型值
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 枚举
     */
    public static LessonTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (LessonTypeEnum value : LessonTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
