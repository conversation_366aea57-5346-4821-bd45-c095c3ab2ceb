package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 证书模板 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 证书模板 Response DTO")
@Data
public class CertificateTemplateRespDTO {

    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "新媒体初级培训证书模板")
    private String name;

    @Schema(description = "证书类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "training")
    private String type;

    @Schema(description = "模板描述", example = "新媒体初级培训证书模板描述")
    private String description;

    @Schema(description = "背景图片文件名", example = "certificate-bg-001.jpg")
    private String background;

    @Schema(description = "背景图片完整URL", example = "https://example.com/images/backgrounds/certificate-bg-001.jpg")
    private String backgroundUrl;

    @Schema(description = "适用课程ID", example = "COURSE_001")
    private String course;

    @Schema(description = "适用课程名称", example = "新媒体初级培训")
    private String courseName;

    @Schema(description = "模板状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "active")
    private String status;

    @Schema(description = "HTML模板内容")
    private String htmlContent;

    @Schema(description = "模板预览图URL", example = "https://example.com/preview/cert-001.jpg")
    private String previewUrl;

    @Schema(description = "字段配置列表")
    private List<CertificateTemplateFieldRespDTO> fields;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建人姓名", example = "管理员")
    private String creatorName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    private LocalDateTime updateTime;

}