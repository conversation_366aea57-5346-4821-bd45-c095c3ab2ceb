package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "商机中心 - 商机日志新增/更新 Request DTO")
public class BusinessLogSaveReqDTO {
    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "商机ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商机ID不能为空")
    private Long businessId;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

    @Schema(description = "操作内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "操作内容不能为空")
    private String content;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作时间")
    private String operateTime;
} 