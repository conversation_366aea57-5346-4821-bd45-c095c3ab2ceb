package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 证书模板创建/更新 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "RPC 服务 - 证书模板创建/更新 Request DTO")
@Data
public class CertificateTemplateSaveReqDTO {

    @Schema(description = "模板ID", example = "1")
    private Long id;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "新媒体初级培训证书模板")
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String name;

    @Schema(description = "证书类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "training")
    @NotBlank(message = "证书类型不能为空")
    private String type;

    @Schema(description = "模板描述", example = "新媒体初级培训证书模板描述")
    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    private String description;

    @Schema(description = "背景图片文件名", example = "certificate-bg-001.jpg")
    @Size(max = 255, message = "背景图片文件名长度不能超过255个字符")
    private String background;

    @Schema(description = "背景图片完整URL", example = "https://example.com/images/backgrounds/certificate-bg-001.jpg")
    @Size(max = 500, message = "背景图片URL长度不能超过500个字符")
    private String backgroundUrl;

    @Schema(description = "适用课程ID", example = "COURSE_001")
    @Size(max = 20, message = "适用课程ID长度不能超过20个字符")
    private String course;

    @Schema(description = "适用课程名称", example = "新媒体初级培训")
    @Size(max = 100, message = "适用课程名称长度不能超过100个字符")
    private String courseName;

    @Schema(description = "模板状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "active")
    @NotBlank(message = "模板状态不能为空")
    private String status;

    @Schema(description = "HTML模板内容")
    private String htmlContent;

    @Schema(description = "字段配置列表")
    @Valid
    private List<CertificateTemplateFieldSaveReqDTO> fields;

}