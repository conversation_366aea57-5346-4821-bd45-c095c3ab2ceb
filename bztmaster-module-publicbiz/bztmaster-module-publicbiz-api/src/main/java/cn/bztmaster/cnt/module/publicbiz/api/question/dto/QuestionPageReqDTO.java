package cn.bztmaster.cnt.module.publicbiz.api.question.dto;

import lombok.Data;

/**
 * 考题分页查询 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class QuestionPageReqDTO {

    /**
     * 页码，从1开始
     */
    private Integer pageNo;

    /**
     * 每页大小，最大100
     */
    private Integer pageSize;

    /**
     * 一级分类名称
     */
    private String level1Name;

    /**
     * 二级分类名称
     */
    private String level2Name;

    /**
     * 三级分类名称
     */
    private String level3Name;

    /**
     * 题型
     */
    private String type;

    /**
     * 业务模块
     */
    private String biz;

    /**
     * 业务模块名称
     */
    private String bizName;

    /**
     * 题干内容关键词搜索
     */
    private String keyword;

}
