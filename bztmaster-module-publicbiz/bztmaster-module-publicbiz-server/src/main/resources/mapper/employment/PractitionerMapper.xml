<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper">

    <sql id="selectFields">
        id, name, phone, id_card, hometown, age, gender, avatar, service_type, experience_years,
        platform_status, rating, agency_id, agency_name, status, current_status, current_order_id,
        total_orders, total_income, customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

</mapper> 