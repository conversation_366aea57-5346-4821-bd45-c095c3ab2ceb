package cn.bztmaster.cnt.module.publicbiz.api.category;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.service.category.ServiceCategoryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 服务分类 API 实现类
 *
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class ServiceCategoryApiImpl implements ServiceCategoryApi {

    @Resource
    private ServiceCategoryService categoryService;

    @Override
    public CommonResult<Boolean> validateCategoryList(Collection<Long> ids) {
        categoryService.validateCategoryList(ids);
        return success(true);
    }

} 