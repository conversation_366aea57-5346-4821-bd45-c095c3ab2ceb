package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionOptionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考题选项表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionOptionMapper extends BaseMapperX<QuestionOptionDO> {

    /**
     * 根据考题ID查询选项列表
     *
     * @param questionId 考题ID
     * @return 选项列表
     */
    default List<QuestionOptionDO> selectByQuestionId(Long questionId) {
        return selectList(new LambdaQueryWrapperX<QuestionOptionDO>()
                .eq(QuestionOptionDO::getQuestionId, questionId)
                .eq(QuestionOptionDO::getDeleted, false)
                .orderByAsc(QuestionOptionDO::getOptionType)
                .orderByAsc(QuestionOptionDO::getSortOrder)
        );
    }

    /**
     * 根据考题ID删除选项
     *
     * @param questionId 考题ID
     * @return 删除数量
     */
    default int deleteByQuestionId(Long questionId) {
        return delete(new LambdaQueryWrapperX<QuestionOptionDO>()
                .eq(QuestionOptionDO::getQuestionId, questionId)
        );
    }

    /**
     * 批量插入选项
     *
     * @param options 选项列表
     * @return 插入数量
     */
    int insertBatch(@Param("options") List<QuestionOptionDO> options);

    /**
     * 根据考题ID列表查询选项
     *
     * @param questionIds 考题ID列表
     * @return 选项列表
     */
    default List<QuestionOptionDO> selectByQuestionIds(List<Long> questionIds) {
        return selectList(new LambdaQueryWrapperX<QuestionOptionDO>()
                .in(QuestionOptionDO::getQuestionId, questionIds)
                .eq(QuestionOptionDO::getDeleted, false)
                .orderByAsc(QuestionOptionDO::getQuestionId)
                .orderByAsc(QuestionOptionDO::getOptionType)
                .orderByAsc(QuestionOptionDO::getSortOrder)
        );
    }

}
