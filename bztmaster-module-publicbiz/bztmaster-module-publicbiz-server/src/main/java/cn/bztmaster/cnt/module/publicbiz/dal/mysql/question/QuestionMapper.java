package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考题主表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionMapper extends BaseMapperX<QuestionDO> {

    /**
     * 分页查询考题列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDO>()
                .likeIfPresent(QuestionDO::getLevel1Name, reqVO.getLevel1Name())
                .likeIfPresent(QuestionDO::getLevel2Name, reqVO.getLevel2Name())
                .likeIfPresent(QuestionDO::getLevel3Name, reqVO.getLevel3Name())
                .eqIfPresent(QuestionDO::getType, reqVO.getType())
                .eqIfPresent(QuestionDO::getBiz, reqVO.getBiz())
                .likeIfPresent(QuestionDO::getBizName, reqVO.getBizName())
                .likeIfPresent(QuestionDO::getTitle, reqVO.getKeyword())
                .or(reqVO.getKeyword() != null, wrapper -> 
                    wrapper.like(QuestionDO::getAnswer, reqVO.getKeyword()))
                .eq(QuestionDO::getDeleted, false)
                .orderByDesc(QuestionDO::getCreateTime)
        );
    }

    /**
     * 根据ID查询考题详情
     *
     * @param id 考题ID
     * @return 考题信息
     */
    default QuestionDO selectById(Long id) {
        return selectOne(new LambdaQueryWrapperX<QuestionDO>()
                .eq(QuestionDO::getId, id)
                .eq(QuestionDO::getDeleted, false)
        );
    }

    /**
     * 查询考题统计数据
     *
     * @param biz 业务模块
     * @param level1Name 一级分类名称
     * @param level2Name 二级分类名称
     * @param level3Name 三级分类名称
     * @return 统计结果
     */
    List<QuestionDO> selectStatistics(@Param("biz") String biz,
                                     @Param("level1Name") String level1Name,
                                     @Param("level2Name") String level2Name,
                                     @Param("level3Name") String level3Name);

    /**
     * 批量插入考题
     *
     * @param questions 考题列表
     * @return 插入数量
     */
    int insertBatch(@Param("questions") List<QuestionDO> questions);

}
