package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_business")
@Schema(description = "商机主表 DO")
public class BusinessDO {
    @TableId
    private Long id;
    /** 租户ID */
    private Long tenantId;
    /** 商机名称 */
    private String name;
    /** 关联客户ID */
    private Long customerId;
    /** 关联客户名称 */
    private String customerName;
    /** 业务模块（高校/培训/认证） */
    private String businessType;
    /** 商机金额(元) */
    private BigDecimal totalPrice;
    /** 预计成交日期 */
    private Date expectedDealDate;
    /** 销售阶段（方案报价/商务谈判/需求分析） */
    private String businessStage;
    /** 商机描述 */
    private String description;
    /** 销售负责人ID */
    private Long ownerUserId;
    /** 销售负责人姓名 */
    private String ownerUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    /** 是否删除，0-未删除，1-已删除 */
    private Boolean deleted;
} 