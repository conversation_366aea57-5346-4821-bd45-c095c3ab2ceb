package cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseChapterDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 课程章节 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface CourseChapterMapper extends BaseMapperX<CourseChapterDO> {

    /**
     * 根据课程ID查询章节列表
     */
    default List<CourseChapterDO> selectListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<CourseChapterDO>()
                .eq(CourseChapterDO::getCourseId, courseId)
                .orderByAsc(CourseChapterDO::getSortOrder)
                .orderByAsc(CourseChapterDO::getId));
    }

    /**
     * 根据课程ID和章节标题查询（用于重复性校验）
     */
    CourseChapterDO selectByCourseIdAndTitle(@Param("courseId") Long courseId, @Param("title") String title, @Param("excludeId") Long excludeId);

    /**
     * 根据课程ID统计章节数量
     */
    Integer countByCourseId(@Param("courseId") Long courseId);

    /**
     * 获取课程下一个排序序号
     */
    Integer getNextSortOrder(@Param("courseId") Long courseId);

    /**
     * 批量删除课程下的所有章节
     */
    void deleteByCourseId(@Param("courseId") Long courseId);
}
