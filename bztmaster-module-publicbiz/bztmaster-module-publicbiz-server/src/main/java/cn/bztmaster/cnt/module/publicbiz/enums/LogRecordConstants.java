package cn.bztmaster.cnt.module.publicbiz.enums;

/**
 * PublicBiz 操作日志枚举
 * 目的：统一管理公共业务相关操作日志类型、子类型、模板
 */
public interface LogRecordConstants {
    // ======================= TEACHER 讲师 =======================
    String TEACHER_TYPE = "TEACHER 讲师";
    String TEACHER_CREATE_SUB_TYPE = "创建讲师";
    String TEACHER_CREATE_SUCCESS = "创建了讲师【{{#teacher.name}}】";
    String TEACHER_UPDATE_SUB_TYPE = "更新讲师";
    String TEACHER_UPDATE_SUCCESS = "更新了讲师【{{#teacher.name}}】: {{#fieldChanges}}{{#certChangeLog != null ? '；' + #certChangeLog : ''}}";
    String TEACHER_DELETE_SUB_TYPE = "删除讲师";
    String TEACHER_DELETE_SUCCESS = "删除了讲师【{{#teacher.name}}】{{#certDeleteLog != null ? '；' + #certDeleteLog : ''}}";

    // ======================= TEACHER_CERT 讲师资质 =======================
    String TEACHER_CERT_TYPE = "TEACHER 讲师资质";
    String TEACHER_CERT_CREATE_SUB_TYPE = "新增讲师资质";
    String TEACHER_CERT_CREATE_SUCCESS = "新增了讲师【{{#cert.teacherId}}】的资质【{{#cert.certName}}】";
    String TEACHER_CERT_DELETE_SUB_TYPE = "删除讲师资质";
    String TEACHER_CERT_DELETE_SUCCESS = "删除了讲师【{{#cert.teacherId}}】的资质【{{#cert.certName}}】";

    // ======================= PARTNER 合作伙伴 =======================
    String PARTNER_TYPE = "PARTNER 合作伙伴";
    String PARTNER_CREATE_SUB_TYPE = "新增合作伙伴";
    String PARTNER_CREATE_SUCCESS = "新增了合作伙伴【{{#partner.name}}】";
    String PARTNER_UPDATE_SUB_TYPE = "更新合作伙伴";
    String PARTNER_UPDATE_SUCCESS = "更新了合作伙伴【{{#partner.name}}】: {_DIFF{#updateReqVO}}";
    String PARTNER_DELETE_SUB_TYPE = "删除合作伙伴";
    String PARTNER_DELETE_SUCCESS = "删除了合作伙伴【{{#partner.name}}】";

    // ======================= SERVICE_PACKAGE 服务套餐 =======================
    String SERVICE_PACKAGE_TYPE = "SERVICE_PACKAGE 服务套餐";
    String SERVICE_PACKAGE_CREATE_SUB_TYPE = "新增服务套餐";
    String SERVICE_PACKAGE_CREATE_SUCCESS = "新增了服务套餐【{{#servicePackage.name}}】";
    String SERVICE_PACKAGE_UPDATE_SUB_TYPE = "更新服务套餐";
    String SERVICE_PACKAGE_UPDATE_SUCCESS = "更新了服务套餐【{{#servicePackage.name}}】: {{#fieldChanges}}{{#carouselChangeLog != null ? '；' + #carouselChangeLog : ''}}{{#featureChangeLog != null ? '；' + #featureChangeLog : ''}}";
    String SERVICE_PACKAGE_DELETE_SUB_TYPE = "删除服务套餐";
    String SERVICE_PACKAGE_DELETE_SUCCESS = "删除了服务套餐【{{#servicePackage.name}}】";
    String SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUB_TYPE = "移动服务套餐到回收站";
    String SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUCCESS = "将服务套餐【{{#servicePackage.name}}】移动到回收站";
    String SERVICE_PACKAGE_STATUS_UPDATE_SUB_TYPE = "更新服务套餐状态";
    String SERVICE_PACKAGE_STATUS_UPDATE_SUCCESS = "批量更新了服务套餐状态为【{{#reqVO.status}}】";

    // ======================= SERVICE_PACKAGE_CAROUSEL 服务套餐轮播图 =======================
    String SERVICE_PACKAGE_CAROUSEL_TYPE = "SERVICE_PACKAGE_CAROUSEL 服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_CREATE_SUB_TYPE = "新增服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_CREATE_SUCCESS = "新增了服务套餐轮播图【{{#carousel.imageUrl}}】";
    String SERVICE_PACKAGE_CAROUSEL_DELETE_SUB_TYPE = "删除服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_DELETE_SUCCESS = "删除了服务套餐轮播图【{{#carousel.imageUrl}}】";

    // ======================= SERVICE_PACKAGE_FEATURE 服务套餐特色标签 =======================
    String SERVICE_PACKAGE_FEATURE_TYPE = "SERVICE_PACKAGE_FEATURE 服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_CREATE_SUB_TYPE = "新增服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_CREATE_SUCCESS = "新增了服务套餐特色标签【{{#feature.featureName}}】";
    String SERVICE_PACKAGE_FEATURE_DELETE_SUB_TYPE = "删除服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_DELETE_SUCCESS = "删除了服务套餐特色标签【{{#feature.featureName}}】";

    // ======================= PRACTITIONER 阿姨 =======================
    String PRACTITIONER_TYPE = "PRACTITIONER 阿姨";
    String PRACTITIONER_CREATE_SUB_TYPE = "新增阿姨";
    String PRACTITIONER_CREATE_SUCCESS = "新增了阿姨【{{#practitioner.name}}】";
    String PRACTITIONER_UPDATE_SUB_TYPE = "更新阿姨";
    String PRACTITIONER_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】: {{#fieldChanges}}{{#qualificationChangeLog != null ? '；' + #qualificationChangeLog : ''}}";
    String PRACTITIONER_DELETE_SUB_TYPE = "删除阿姨";
    String PRACTITIONER_DELETE_SUCCESS = "删除了阿姨【{{#practitioner.name}}】";
    String PRACTITIONER_STATUS_UPDATE_SUB_TYPE = "更新阿姨状态";
    String PRACTITIONER_STATUS_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】的状态为【{{#reqVO.platformStatus}}】";
    String PRACTITIONER_RATING_UPDATE_SUB_TYPE = "更新阿姨评级";
    String PRACTITIONER_RATING_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】的评级为【{{#reqVO.newRating}}】";

    // ======================= PRACTITIONER_QUALIFICATION 阿姨资质文件 =======================
    String PRACTITIONER_QUALIFICATION_TYPE = "PRACTITIONER_QUALIFICATION 阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_CREATE_SUB_TYPE = "新增阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_CREATE_SUCCESS = "新增了阿姨【{{#qualification.practitionerId}}】的资质文件【{{#qualification.fileName}}】";
    String PRACTITIONER_QUALIFICATION_DELETE_SUB_TYPE = "删除阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_DELETE_SUCCESS = "删除了阿姨【{{#qualification.practitionerId}}】的资质文件【{{#qualification.fileName}}】";

    // ======================= QUESTION 考题管理 =======================
    String QUESTION_TYPE = "QUESTION 考题管理";
    String QUESTION_CREATE_SUB_TYPE = "创建考题";
    String QUESTION_CREATE_SUCCESS = "创建了考题【{{#question.title}}】，题型为【{{#question.type}}】";
    String QUESTION_UPDATE_SUB_TYPE = "更新考题";
    String QUESTION_UPDATE_SUCCESS = "更新了考题【{{#question.title}}】: {_DIFF{#updateReqVO}}";
    String QUESTION_DELETE_SUB_TYPE = "删除考题";
    String QUESTION_DELETE_SUCCESS = "删除了考题【{{#question.title}}】";

    // ======================= QUESTION_CATEGORY 考题分类管理 =======================
    String QUESTION_CATEGORY_TYPE = "QUESTION_CATEGORY 考题分类管理";
    String QUESTION_CATEGORY_CREATE_SUB_TYPE = "创建考题分类";
    String QUESTION_CATEGORY_CREATE_SUCCESS = "创建了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】";
    String QUESTION_CATEGORY_UPDATE_SUB_TYPE = "更新考题分类";
    String QUESTION_CATEGORY_UPDATE_SUCCESS = "更新了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】: {_DIFF{#updateReqVO}}";
    String QUESTION_CATEGORY_DELETE_SUB_TYPE = "删除考题分类";
    String QUESTION_CATEGORY_DELETE_SUCCESS = "删除了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】";
}