package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;

import java.util.Date;

/**
 * 合作伙伴 DO 实体
 */
@TableName("publicbiz_partner")
@Data
@Schema(description = "合作伙伴 DO")
public class PartnerDO {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 机构名称 */
    private String name;

    /** 我方负责人ID */
    private Long owner;
    /** 我方负责人昵称 */
    private String ownerName;
    /** 机构类型 */
    private String type;
    /** 机构简称 */
    private String shortName;
    /** 业务模块 */
    private String biz;
    /** 合作状态 */
    private String status;
    /** 风险等级 */
    private String risk;
    /** 法人代表 */
    private String legalPerson;
    /** 成立日期 */
    private Date foundationDate;
    /** 统一社会信用代码 */
    private String creditCode;
    /** 注册地址 */
    private String registerAddress;
    /** 经营地址 */
    private String businessAddress;
    /** 主营业务 */
    private String mainBusiness;
    /** 主要联系人 */
    private String contactName;
    /** 联系电话 */
    private String contactPhone;
    /** 当前评级（星级） */
    private Integer rating;
    /** 合作模式 */
    private String cooperationMode;
    /** 合同编号 */
    private String contractNo;
    /** 合同开始日期 */
    private Date contractStart;
    /** 合同结束日期 */
    private Date contractEnd;
    /** 保证金 */
    private java.math.BigDecimal deposit;
    /** 续约提醒日期 */
    private Date renewDate;
    /** 对公账户名 */
    private String accountName;
    /** 结算周期 */
    private String settlementCycle;
    /** 开户银行 */
    private String bankName;
    /** 银行账号 */
    private String bankAccount;
    /** 资质文件（URL或ID） */
    private String qualificationFile;
    /** 开票类型 */
    private String invoiceType;
    /** 开票名称 */
    private String invoiceName;
    /** 纳税人识别号 */
    private String taxId;
    /** 社会组织代码 */
    private String orgCode;
    /** 开票地址 */
    private String invoiceAddress;
    /** 开票电话 */
    private String invoicePhone;
    /** 开票开户银行 */
    private String invoiceBank;
    /** 开票银行账号 */
    private String invoiceBankAccount;
    /** 开票邮箱 */
    private String invoiceEmail;
    /** 开票联系人 */
    private String invoiceContact;
    /** 开票资质文件（URL或ID） */
    private String invoiceQualificationFile;
    /** 开票备注 */
    private String invoiceRemark;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 创建者 */
    private String creator;
    /** 更新者 */
    private String updater;
    private Boolean deleted;
    /**
     * 租户编号
     */
    private Long tenantId;

}
