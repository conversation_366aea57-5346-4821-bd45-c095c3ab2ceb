package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 线索跟进记录 DO
 * 
 * 对应表：lead_follow_up_log
 *
 * <AUTHOR>
 */
@TableName("publicbiz_lead_follow_up_log")
@KeySequence("lead_follow_up_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeadFollowUpLogDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    /**
     * 关联的线索ID
     */
    @NotEmpty(message = "线索ID不能为空")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    /**
     * 跟进内容详情
     */
    @NotEmpty(message = "跟进内容不能为空")
    private String followUpContent;

    /**
     * 创建人姓名
     */
    @Size(max = 64, message = "创建人姓名长度不能超过64")
    private String creatorName;

}