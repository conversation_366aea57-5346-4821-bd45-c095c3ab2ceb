package cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplatePageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateListReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateStatisticsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplatePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateListReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.certificateTemplate.CertificateTemplateConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;
import cn.bztmaster.cnt.module.publicbiz.service.certificateTemplate.CertificateTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 证书模板 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CertificateTemplateApiImpl implements CertificateTemplateApi {

    @Resource
    private CertificateTemplateService certificateTemplateService;

    @Override
    public CommonResult<PageResult<CertificateTemplateRespDTO>> pageCertificateTemplate(@Valid CertificateTemplatePageReqDTO pageReqDTO) {
        CertificateTemplatePageReqVO pageReqVO = BeanUtils.toBean(pageReqDTO, CertificateTemplatePageReqVO.class);
        PageResult<CertificateTemplateDO> pageResult = certificateTemplateService.getCertificateTemplatePage(pageReqVO);
        PageResult<CertificateTemplateRespDTO> dtoPageResult = BeanUtils.toBean(pageResult, PageResult.class);
        dtoPageResult.setList(CertificateTemplateConvert.INSTANCE.convertToDTOList(pageResult.getList()));
        return success(dtoPageResult);
    }

    @Override
    public CommonResult<CertificateTemplateRespDTO> getCertificateTemplate(Long id) {
        CertificateTemplateDO certificateTemplate = certificateTemplateService.getCertificateTemplate(id);
        return success(CertificateTemplateConvert.INSTANCE.convertToDTO(certificateTemplate));
    }

    @Override
    public CommonResult<Long> createCertificateTemplate(@Valid CertificateTemplateSaveReqDTO createReqDTO) {
        CertificateTemplateSaveReqVO createReqVO = BeanUtils.toBean(createReqDTO, CertificateTemplateSaveReqVO.class);
        return success(certificateTemplateService.createCertificateTemplate(createReqVO));
    }

    @Override
    public CommonResult<Boolean> updateCertificateTemplate(@Valid CertificateTemplateSaveReqDTO updateReqDTO) {
        CertificateTemplateSaveReqVO updateReqVO = BeanUtils.toBean(updateReqDTO, CertificateTemplateSaveReqVO.class);
        certificateTemplateService.updateCertificateTemplate(updateReqVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> deleteCertificateTemplate(Long id) {
        certificateTemplateService.deleteCertificateTemplate(id);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateCertificateTemplateStatus(Long id, String status) {
        certificateTemplateService.updateCertificateTemplateStatus(id, status);
        return success(true);
    }

    @Override
    public CommonResult<List<CertificateTemplateRespDTO>> listCertificateTemplate(@Valid CertificateTemplateListReqDTO listReqDTO) {
        CertificateTemplateListReqVO listReqVO = BeanUtils.toBean(listReqDTO, CertificateTemplateListReqVO.class);
        List<CertificateTemplateDO> list = certificateTemplateService.getCertificateTemplateList(listReqVO);
        return success(CertificateTemplateConvert.INSTANCE.convertToDTOList(list));
    }

    @Override
    public CommonResult<CertificateTemplateStatisticsRespDTO> getCertificateTemplateStatistics() {
        return success(BeanUtils.toBean(certificateTemplateService.getCertificateTemplateStatistics(), CertificateTemplateStatisticsRespDTO.class));
    }

}