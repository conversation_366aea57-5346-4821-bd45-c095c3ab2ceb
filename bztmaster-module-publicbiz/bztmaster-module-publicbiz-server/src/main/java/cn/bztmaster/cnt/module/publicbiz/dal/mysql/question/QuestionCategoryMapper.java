package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 考题分类表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionCategoryMapper extends BaseMapperX<QuestionCategoryDO> {

    /**
     * 分页查询分类列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<QuestionCategoryDO> selectPage(QuestionCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eqIfPresent(QuestionCategoryDO::getBiz, reqVO.getBiz())
                .inIfPresent(QuestionCategoryDO::getLevel, reqVO.getLevel())
                .eqIfPresent(QuestionCategoryDO::getParentId, reqVO.getParentId())
                .eq(QuestionCategoryDO::getDeleted, false)
                .orderByAsc(QuestionCategoryDO::getLevel)
                .orderByAsc(QuestionCategoryDO::getSortOrder)
        );
    }

    /**
     * 查询分类列表（不分页）
     *
     * @param biz 业务模块
     * @param levels 分类层级列表
     * @param parentId 父级分类ID
     * @return 分类列表
     */
    default List<QuestionCategoryDO> selectList(String biz, List<Integer> levels, Long parentId) {
        return selectList(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eqIfPresent(QuestionCategoryDO::getBiz, biz)
                .inIfPresent(QuestionCategoryDO::getLevel, levels)
                .eqIfPresent(QuestionCategoryDO::getParentId, parentId)
                .eq(QuestionCategoryDO::getDeleted, false)
                .orderByAsc(QuestionCategoryDO::getLevel)
                .orderByAsc(QuestionCategoryDO::getSortOrder)
        );
    }

    /**
     * 根据ID查询分类详情
     *
     * @param id 分类ID
     * @return 分类信息
     */
    default QuestionCategoryDO selectById(Long id) {
        return selectOne(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eq(QuestionCategoryDO::getId, id)
                .eq(QuestionCategoryDO::getDeleted, false)
        );
    }

    /**
     * 检查分类代码是否重复
     *
     * @param level1Code 一级分类代码
     * @param level2Code 二级分类代码
     * @param level3Code 三级分类代码
     * @param excludeId 排除的ID
     * @return 分类信息
     */
    default QuestionCategoryDO selectByCode(String level1Code, String level2Code, String level3Code, Long excludeId) {
        return selectOne(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eq(QuestionCategoryDO::getLevel1Code, level1Code)
                .eq(QuestionCategoryDO::getLevel2Code, level2Code)
                .eq(QuestionCategoryDO::getLevel3Code, level3Code)
                .neIfPresent(QuestionCategoryDO::getId, excludeId)
                .eq(QuestionCategoryDO::getDeleted, false)
        );
    }

    /**
     * 根据父级ID查询子分类列表
     *
     * @param parentId 父级分类ID
     * @return 子分类列表
     */
    default List<QuestionCategoryDO> selectByParentId(Long parentId) {
        return selectList(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eq(QuestionCategoryDO::getParentId, parentId)
                .eq(QuestionCategoryDO::getDeleted, false)
        );
    }

    /**
     * 根据分类名称和代码组合查询分类
     *
     * @param level1Name 一级分类名称
     * @param level1Code 一级分类代码
     * @param level2Name 二级分类名称
     * @param level2Code 二级分类代码
     * @param level3Name 三级分类名称
     * @param level3Code 三级分类代码
     * @param certName 认定点名称
     * @param certCode 认定点代码
     * @return 分类信息
     */
    default QuestionCategoryDO selectByNameAndCode(String level1Name, String level1Code,
                                                   String level2Name, String level2Code,
                                                   String level3Name, String level3Code,
                                                   String certName, String certCode) {
        return selectOne(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .eq(QuestionCategoryDO::getLevel1Name, level1Name)
                .eq(QuestionCategoryDO::getLevel1Code, level1Code)
                .eq(QuestionCategoryDO::getLevel2Name, level2Name)
                .eq(QuestionCategoryDO::getLevel2Code, level2Code)
                .eq(QuestionCategoryDO::getLevel3Name, level3Name)
                .eq(QuestionCategoryDO::getLevel3Code, level3Code)
                .eq(QuestionCategoryDO::getCertName, certName)
                .eq(QuestionCategoryDO::getCertCode, certCode)
                .eq(QuestionCategoryDO::getDeleted, false)
        );
    }

}
