package cn.bztmaster.cnt.module.publicbiz.dal.mysql.business;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.BusinessPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;

/**
 * 商机主表 Mapper
 */
@Mapper
public interface BusinessMapper extends BaseMapperX<BusinessDO> {
    
    /**
     * 分页查询商机列表
     */
    default PageResult<BusinessDO> selectPage(BusinessPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BusinessDO>()
                .eqIfPresent(BusinessDO::getId, reqVO.getId())
                .eqIfPresent(BusinessDO::getTenantId, reqVO.getTenantId())
                .likeIfPresent(BusinessDO::getName, reqVO.getName())
                .eqIfPresent(BusinessDO::getCustomerId, reqVO.getCustomerId())
                .likeIfPresent(BusinessDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(BusinessDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(BusinessDO::getTotalPrice, reqVO.getTotalPrice())
                .eqIfPresent(BusinessDO::getBusinessStage, reqVO.getBusinessStage())
                .eqIfPresent(BusinessDO::getOwnerUserId, reqVO.getOwnerUserId())
                .likeIfPresent(BusinessDO::getOwnerUserName, reqVO.getOwnerUserName())
                .eqIfPresent(BusinessDO::getCreator, reqVO.getCreator())
                .eqIfPresent(BusinessDO::getUpdater, reqVO.getUpdater())
                .eq(BusinessDO::getDeleted, false)
                .orderByDesc(BusinessDO::getId)
        );
    }
} 