package cn.bztmaster.cnt.module.publicbiz.api.question;

import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 考题 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class QuestionApiImpl implements QuestionApi {

    @Resource
    private QuestionService questionService;

    @Override
    public QuestionRespDTO getQuestion(Long id) {
        QuestionDO question = questionService.getQuestionDO(id);
        return BeanUtils.toBean(question, QuestionRespDTO.class);
    }

    @Override
    public List<QuestionRespDTO> getQuestionList(Collection<Long> ids) {
        List<QuestionDO> questions = questionService.getQuestionDOList(ids);
        return BeanUtils.toBean(questions, QuestionRespDTO.class);
    }

    @Override
    public List<QuestionRespDTO> getQuestionPage(QuestionPageReqDTO reqDTO) {
        QuestionPageReqVO reqVO = BeanUtils.toBean(reqDTO, QuestionPageReqVO.class);
        // TODO: 实现分页查询并返回列表
        return null;
    }

    @Override
    public Long createQuestion(QuestionSaveReqDTO reqDTO) {
        QuestionSaveReqVO reqVO = BeanUtils.toBean(reqDTO, QuestionSaveReqVO.class);
        return questionService.createQuestion(reqVO);
    }

    @Override
    public void updateQuestion(QuestionSaveReqDTO reqDTO) {
        QuestionSaveReqVO reqVO = BeanUtils.toBean(reqDTO, QuestionSaveReqVO.class);
        questionService.updateQuestion(reqVO);
    }

    @Override
    public void deleteQuestion(Long id) {
        questionService.deleteQuestion(id);
    }

    @Override
    public void validateQuestionExists(Long id) {
        questionService.validateQuestionExists(id);
    }

}
