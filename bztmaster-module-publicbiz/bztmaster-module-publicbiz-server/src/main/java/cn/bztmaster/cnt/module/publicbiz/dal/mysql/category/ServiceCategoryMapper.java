package cn.bztmaster.cnt.module.publicbiz.dal.mysql.category;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.category.ServiceCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 服务分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ServiceCategoryMapper extends BaseMapperX<ServiceCategoryDO> {

    default List<ServiceCategoryDO> selectList(ServiceCategoryListReqVO listReqVO) {
        return selectList(new LambdaQueryWrapperX<ServiceCategoryDO>()
                .likeIfPresent(ServiceCategoryDO::getName, listReqVO.getName())
                .eqIfPresent(ServiceCategoryDO::getParentId, listReqVO.getParentId())
                .inIfPresent(ServiceCategoryDO::getId, listReqVO.getParentIds())
                .eqIfPresent(ServiceCategoryDO::getStatus, listReqVO.getStatus())
                .orderByDesc(ServiceCategoryDO::getId));
    }

    default PageResult<ServiceCategoryDO> selectPage(ServiceCategoryListReqVO listReqVO) {
        return selectPage(listReqVO, new LambdaQueryWrapperX<ServiceCategoryDO>()
                .likeIfPresent(ServiceCategoryDO::getName, listReqVO.getName())
                .eqIfPresent(ServiceCategoryDO::getParentId, listReqVO.getParentId())
                .inIfPresent(ServiceCategoryDO::getId, listReqVO.getParentIds())
                .eqIfPresent(ServiceCategoryDO::getStatus, listReqVO.getStatus())
                .orderByDesc(ServiceCategoryDO::getId));
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(ServiceCategoryDO::getParentId, parentId);
    }

    default List<ServiceCategoryDO> selectListByStatus(Integer status) {
        return selectList(ServiceCategoryDO::getStatus, status);
    }

    default List<ServiceCategoryDO> selectListByIdAndStatus(Collection<Long> ids, Integer status) {
        return selectList(new LambdaQueryWrapperX<ServiceCategoryDO>()
                .in(ServiceCategoryDO::getId, ids)
                .eq(ServiceCategoryDO::getStatus, status));
    }

} 