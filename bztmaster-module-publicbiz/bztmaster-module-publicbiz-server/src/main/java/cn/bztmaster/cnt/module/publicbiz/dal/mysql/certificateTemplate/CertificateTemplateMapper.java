package cn.bztmaster.cnt.module.publicbiz.dal.mysql.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplatePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 证书模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CertificateTemplateMapper extends BaseMapperX<CertificateTemplateDO> {

    /**
     * 分页查询证书模板
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<CertificateTemplateDO> selectPage(CertificateTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CertificateTemplateDO>()
                .eqIfPresent(CertificateTemplateDO::getType, reqVO.getType())
                .eqIfPresent(CertificateTemplateDO::getStatus, reqVO.getStatus())
                .and(reqVO.getKeyword() != null, w -> w
                        .like(CertificateTemplateDO::getName, reqVO.getKeyword())
                        .or()
                        .like(CertificateTemplateDO::getDescription, reqVO.getKeyword()))
                .orderByDesc(CertificateTemplateDO::getId));
    }

    /**
     * 查询证书模板列表
     *
     * @param status 状态
     * @return 模板列表
     */
    default List<CertificateTemplateDO> selectList(String status) {
        return selectList(new LambdaQueryWrapperX<CertificateTemplateDO>()
                .eqIfPresent(CertificateTemplateDO::getStatus, status)
                .orderByDesc(CertificateTemplateDO::getId));
    }

    /**
     * 根据名称查询证书模板
     *
     * @param name 模板名称
     * @return 证书模板
     */
    default CertificateTemplateDO selectByName(String name) {
        return selectOne(CertificateTemplateDO::getName, name);
    }

    /**
     * 统计各状态的模板数量
     *
     * @param status 状态
     * @return 数量
     */
    default Long selectCountByStatus(String status) {
        return selectCount(CertificateTemplateDO::getStatus, status);
    }

    /**
     * 统计总数量
     *
     * @return 总数量
     */
    default Long selectTotalCount() {
        return selectCount();
    }

}