package cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseAttachmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 课程附件 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface CourseAttachmentMapper extends BaseMapperX<CourseAttachmentDO> {

    /**
     * 根据课程ID查询附件列表
     */
    default List<CourseAttachmentDO> selectListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<CourseAttachmentDO>()
                .eq(CourseAttachmentDO::getCourseId, courseId)
                .orderByDesc(CourseAttachmentDO::getCreateTime));
    }

    /**
     * 根据课程ID和附件名称查询（用于重复性校验）
     */
    CourseAttachmentDO selectByCourseIdAndName(@Param("courseId") Long courseId, @Param("attachmentName") String attachmentName, @Param("excludeId") Long excludeId);

    /**
     * 根据课程ID统计附件数量
     */
    Integer countByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据附件类型统计数量
     */
    Integer countByAttachmentType(@Param("attachmentType") String attachmentType);

    /**
     * 批量删除课程下的所有附件
     */
    void deleteByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据文件URL查询附件
     */
    CourseAttachmentDO selectByFileUrl(@Param("fileUrl") String fileUrl);

    /**
     * 根据课程ID和附件类型查询附件列表
     */
    List<CourseAttachmentDO> selectByCourseIdAndType(@Param("courseId") Long courseId, @Param("attachmentType") String attachmentType);

    /**
     * 计算课程附件总大小
     */
    Long sumFileSizeByCourseId(@Param("courseId") Long courseId);
}
