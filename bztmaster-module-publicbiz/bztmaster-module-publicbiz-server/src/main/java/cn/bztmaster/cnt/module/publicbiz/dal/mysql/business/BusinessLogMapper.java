package cn.bztmaster.cnt.module.publicbiz.dal.mysql.business;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.BusinessLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商机操作日志表 Mapper
 */
@Mapper
public interface BusinessLogMapper extends BaseMapper<BusinessLogDO> {
    
    /**
     * 分页查询商机操作日志
     */
    PageResult<BusinessLogDO> selectPage(@Param("reqVO")BusinessLogPageReqVO reqVO);
} 