package cn.bztmaster.cnt.module.publicbiz.dal.mysql.certificateTemplate;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateFieldDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 证书模板字段配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CertificateTemplateFieldMapper extends BaseMapperX<CertificateTemplateFieldDO> {

    /**
     * 根据模板ID查询字段列表
     *
     * @param templateId 模板ID
     * @return 字段列表
     */
    default List<CertificateTemplateFieldDO> selectListByTemplateId(Long templateId) {
        return selectList(new LambdaQueryWrapperX<CertificateTemplateFieldDO>()
                .eq(CertificateTemplateFieldDO::getTemplateId, templateId)
                .orderByAsc(CertificateTemplateFieldDO::getSortOrder));
    }

    /**
     * 根据模板ID物理删除字段（真正从数据库中删除记录）
     *
     * @param templateId 模板ID
     * @return 删除数量
     */
    @Delete("DELETE FROM publicbiz_certificate_template_field WHERE template_id = #{templateId}")
    int deleteByTemplateIdPhysically(@Param("templateId") Long templateId);

    /**
     * 根据模板ID删除字段（现在使用物理删除）
     *
     * @param templateId 模板ID
     * @return 删除数量
     */
    default int deleteByTemplateId(Long templateId) {
        // 使用物理删除替代逻辑删除，避免数据冗余和唯一约束冲突
        return deleteByTemplateIdPhysically(templateId);
    }

    /**
     * 根据模板ID和字段ID查询字段
     *
     * @param templateId 模板ID
     * @param fieldId    字段ID
     * @return 字段配置
     */
    default CertificateTemplateFieldDO selectByTemplateIdAndFieldId(Long templateId, String fieldId) {
        return selectOne(new LambdaQueryWrapperX<CertificateTemplateFieldDO>()
                .eq(CertificateTemplateFieldDO::getTemplateId, templateId)
                .eq(CertificateTemplateFieldDO::getFieldId, fieldId));
    }

    /**
     * 根据模板ID统计字段数量
     *
     * @param templateId 模板ID
     * @return 字段数量
     */
    default Long selectCountByTemplateId(Long templateId) {
        return selectCount(CertificateTemplateFieldDO::getTemplateId, templateId);
    }

}