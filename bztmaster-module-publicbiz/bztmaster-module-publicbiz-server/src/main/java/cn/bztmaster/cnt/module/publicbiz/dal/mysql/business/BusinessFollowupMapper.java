package cn.bztmaster.cnt.module.publicbiz.dal.mysql.business;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.BusinessFollowupPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessFollowupDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机跟进表 Mapper
 */
@Mapper
public interface BusinessFollowupMapper extends BaseMapper<BusinessFollowupDO> {
    
    /**
     * 分页查询商机跟进记录
     */
    PageResult<BusinessFollowupDO> selectPage(@Param("reqVO") BusinessFollowupPageReqVO reqVO);
    
    /**
     * 根据商机ID查询跟进记录列表
     */
    List<BusinessFollowupDO> selectListByBusinessId(@Param("businessId")Long businessId);
} 