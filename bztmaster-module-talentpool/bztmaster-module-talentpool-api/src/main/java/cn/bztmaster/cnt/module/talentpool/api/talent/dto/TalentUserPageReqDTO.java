package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;

/**
 * 人才用户分页查询请求 DTO
 *
 * 用于分页查询人才库用户列表，支持按关键字、来源、标签、状态等筛选。
 *
 * <AUTHOR>
 */
@Data
public class TalentUserPageReqDTO extends PageParam {

    /**
     * 关键字（姓名/手机号/身份证）
     */
    @Schema(description = "关键字（姓名/手机号/身份证）", example = "张三")
    private String keyword;

    /**
     * 用户来源
     */
    @Schema(description = "用户来源", example = "高校实习小程序")
    private String source;

    /**
     * 用户标签
     */
    @Schema(description = "用户标签", example = "已实名")
    private String tag;

    /**
     * 用户状态（正常/待合并/已禁用）
     */
    @Schema(description = "用户状态", example = "正常")
    private String status;

    /**
     * 是否平台自营（true-是，false-否）
     */
    @Schema(description = "是否平台自营（true-是，false-否）", example = "true")
    private Boolean isSelfSupport;

    /**
     * 所属机构ID
     */
    @Schema(description = "所属机构ID", example = "1")
    private Long orgId;

    /**
     * 页码，默认1
     */
    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;

    /**
     * 每页条数，默认10
     */
    @Schema(description = "每页条数", example = "10")
    private Integer pageSize = 10;
}