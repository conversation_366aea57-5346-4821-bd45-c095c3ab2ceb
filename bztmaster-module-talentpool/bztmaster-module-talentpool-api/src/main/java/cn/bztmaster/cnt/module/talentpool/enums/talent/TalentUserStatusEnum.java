package cn.bztmaster.cnt.module.talentpool.enums.talent;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人才用户状态枚举
 *
 * 用于标识人才库用户的状态，如正常、待合并、已禁用。
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TalentUserStatusEnum {
    /**
     * 正常
     */
    NORMAL("正常"),
    /**
     * 待合并
     */
    TO_MERGE("待合并"),
    /**
     * 已禁用
     */
    DISABLED("已禁用");

    private final String label;
} 