package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 编辑人才用户请求 DTO
 *
 * 用于编辑人才库用户的基本信息。
 *
 * <AUTHOR>
 */
@Data
public class TalentUserUpdateReqDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "10001")
    @NotNull(message = "用户ID不能为空")
    private Long id;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    @NotNull(message = "姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "138****1234")
    @NotNull(message = "手机号不能为空")
    private String phone;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号", example = "110101199001011234")
    @NotNull(message = "身份证号不能为空")
    private String identityId;

    /**
     * 用户来源
     */
    @Schema(description = "用户来源", example = "高校实习小程序")
    private String source;

    /**
     * 用户标签
     */
    @Schema(description = "用户标签", example = "[\"认证:高级母婴护理\", \"已实名\"]")
    private List<String> tags;

    /**
     * 用户状态
     */
    @Schema(description = "用户状态", example = "正常")
    private String status;

    /**
     * 档案完整度
     */
    @Schema(description = "档案完整度", example = "95")
    private Integer completeness;

    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "性别")
    private String gender;
    @Schema(description = "出生日期")
    private java.util.Date birthDate;
    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "所属机构ID")
    private Long orgId;
    @Schema(description = "所属机构名称")
    private String orgName;
    @Schema(description = "人才来源")
    private String talentSource;
    @Schema(description = "是否为平台自营（0-否，1-是）")
    private Boolean isSelfSupport;

    // 子表
    @Schema(description = "教育背景列表")
    private java.util.List<TalentUserCreateReqDTO.EducationDTO> educationList;
    @Schema(description = "校园实践列表")
    private java.util.List<TalentUserCreateReqDTO.CampusPracticeDTO> campusPracticeList;
    @Schema(description = "实习经历列表")
    private java.util.List<TalentUserCreateReqDTO.InternshipDTO> internshipList;
    @Schema(description = "项目经历列表")
    private java.util.List<TalentUserCreateReqDTO.ProjectDTO> projectList;
    @Schema(description = "培训经历列表")
    private java.util.List<TalentUserCreateReqDTO.TrainingDTO> trainingList;
    @Schema(description = "技能列表")
    private java.util.List<TalentUserCreateReqDTO.SkillDTO> skillList;
    @Schema(description = "证书列表")
    private java.util.List<TalentUserCreateReqDTO.CertificateDTO> certificateList;
    @Schema(description = "求职申请列表")
    private java.util.List<TalentUserCreateReqDTO.JobApplicationDTO> jobApplicationList;
    @Schema(description = "工作经历列表")
    private java.util.List<TalentUserCreateReqDTO.EmploymentDTO> employmentList;
    @Schema(description = "用户标签明细列表")
    private java.util.List<TalentUserCreateReqDTO.UserTagDTO> userTagList;
}